# TrainAI 环境变量配置模板
# 复制此文件为 .env 并修改相应的值

# ================================
# 应用基础配置
# ================================
APP_NAME=TrainAI
APP_VERSION=1.0.0
DEBUG=true
SECRET_KEY=your-super-secret-key-change-this-in-production
ENVIRONMENT=development

# ================================
# 服务端口配置
# ================================
BACKEND_PORT=8000
FRONTEND_PORT=3000

# ================================
# 数据库配置
# ================================
# MongoDB配置
MONGODB_URL=mongodb://localhost:27017/trainai
MONGODB_DB_NAME=trainai

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=

# ================================
# 对象存储配置 (MinIO)
# ================================
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET=trainai-storage
MINIO_SECURE=false

# ================================
# 认证配置
# ================================
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
ALGORITHM=HS256

# ================================
# 文件上传配置
# ================================
MAX_FILE_SIZE=1073741824  # 1GB in bytes
ALLOWED_EXTENSIONS=csv,json,parquet,jpg,jpeg,png,bmp,txt,pkl,pth,onnx

# ================================
# GPU和计算资源配置
# ================================
CUDA_VISIBLE_DEVICES=0
MAX_GPU_MEMORY=8192  # MB
MAX_CONCURRENT_TRAINING=3
DEFAULT_BATCH_SIZE=32

# ================================
# 日志配置
# ================================
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_FILE=logs/trainai.log

# ================================
# 前端配置
# ================================
REACT_APP_API_URL=http://localhost:8000
REACT_APP_WS_URL=ws://localhost:8000
REACT_APP_UPLOAD_MAX_SIZE=1073741824

# ================================
# 邮件配置 (可选)
# ================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM=TrainAI <<EMAIL>>

# ================================
# 监控配置 (可选)
# ================================
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001

# ================================
# 开发工具配置
# ================================
# 是否启用API文档
ENABLE_DOCS=true
# 是否启用CORS (开发环境)
ENABLE_CORS=true
# 允许的CORS源
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
