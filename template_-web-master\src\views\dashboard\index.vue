<template>
  <div class="dashboard">
    <el-row :gutter="20">
      <!-- 统计卡片 -->
      <el-col :xs="24" :sm="12" :lg="6" v-for="item in statsData" :key="item.title">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-info">
              <h3>{{ item.value }}</h3>
              <p>{{ item.title }}</p>
            </div>
            <div class="stats-icon">
              <el-icon :size="40" :color="item.color">
                <component :is="item.icon" />
              </el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 图表区域 -->
      <el-col :xs="24" :lg="12">
        <el-card title="访问趋势">
          <template #header>
            <span>访问趋势</span>
          </template>
          <div class="chart-container">
            <div class="chart-placeholder">
              <el-icon :size="60" color="#ddd">
                <TrendCharts />
              </el-icon>
              <p>图表区域（可集成 ECharts 等图表库）</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :lg="12">
        <el-card title="系统信息">
          <template #header>
            <span>系统信息</span>
          </template>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="操作系统">{{ systemInfo.os }}</el-descriptions-item>
            <el-descriptions-item label="浏览器">{{ systemInfo.browser }}</el-descriptions-item>
            <el-descriptions-item label="Vue版本">{{ systemInfo.vue }}</el-descriptions-item>
            <el-descriptions-item label="Vite版本">{{ systemInfo.vite }}</el-descriptions-item>
            <el-descriptions-item label="运行时间">{{ systemInfo.uptime }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 快捷操作 -->
      <el-col :span="24">
        <el-card title="快捷操作">
          <template #header>
            <span>快捷操作</span>
          </template>
          <div class="quick-actions">
            <el-button
              v-for="action in quickActions"
              :key="action.name"
              :type="action.type"
              :icon="action.icon"
              @click="handleQuickAction(action.action)"
            >
              {{ action.name }}
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { formatDate } from '@/utils'

// 快捷操作类型
interface QuickAction {
  name: string
  type: 'primary' | 'success' | 'warning' | 'info' | 'danger' | 'default' | 'text'
  icon: string
  action: string
}

const router = useRouter()

// 统计数据
const statsData = ref([
  {
    title: '总用户数',
    value: '1,234',
    icon: 'User',
    color: '#409eff'
  },
  {
    title: '今日访问',
    value: '567',
    icon: 'View',
    color: '#67c23a'
  },
  {
    title: '订单数量',
    value: '89',
    icon: 'ShoppingCart',
    color: '#e6a23c'
  },
  {
    title: '收入金额',
    value: '¥12,345',
    icon: 'Money',
    color: '#f56c6c'
  }
])

// 系统信息
const systemInfo = ref({
  os: navigator.platform,
  browser: navigator.userAgent.split(' ').pop() || 'Unknown',
  vue: '3.4.0',
  vite: '5.0.10',
  uptime: '0天0小时0分钟'
})

// 快捷操作
const quickActions = ref<QuickAction[]>([
  { name: '用户管理', type: 'primary', icon: 'User', action: 'user' },
  { name: '系统设置', type: 'success', icon: 'Setting', action: 'settings' },
  { name: '数据统计', type: 'warning', icon: 'DataAnalysis', action: 'analytics' },
  { name: '日志查看', type: 'info', icon: 'Document', action: 'logs' }
])

// 处理快捷操作
const handleQuickAction = (action: string) => {
  switch (action) {
    case 'user':
      router.push('/user')
      break
    case 'settings':
      router.push('/system')
      break
    case 'analytics':
      ElMessage.info('数据统计功能开发中...')
      break
    case 'logs':
      ElMessage.info('日志查看功能开发中...')
      break
  }
}

// 更新运行时间
const updateUptime = () => {
  const startTime = Date.now()
  setInterval(() => {
    const now = Date.now()
    const diff = now - startTime
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    systemInfo.value.uptime = `${days}天${hours}小时${minutes}分钟`
  }, 60000)
}

onMounted(() => {
  updateUptime()
})
</script>

<style lang="scss" scoped>
.dashboard {
  .stats-card {
    .stats-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .stats-info {
        h3 {
          margin: 0 0 8px 0;
          font-size: 24px;
          font-weight: bold;
          color: var(--el-text-color-primary);
        }
        
        p {
          margin: 0;
          color: var(--el-text-color-regular);
          font-size: 14px;
        }
      }
    }
  }
  
  .chart-container {
    height: 300px;
    
    .chart-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: var(--el-text-color-placeholder);
      
      p {
        margin-top: 16px;
        font-size: 14px;
      }
    }
  }
  
  .quick-actions {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
  }
}
</style>
