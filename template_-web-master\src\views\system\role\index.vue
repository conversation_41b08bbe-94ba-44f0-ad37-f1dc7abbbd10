<template>
  <div class="role-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>角色管理</span>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增角色
          </el-button>
        </div>
      </template>
      
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="角色名称" />
        <el-table-column prop="code" label="角色编码" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button type="warning" size="small" @click="handlePermission(row)">权限</el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDate } from '@/utils'
import type { RoleData } from '@/types'

const tableData = ref<RoleData[]>([])
const loading = ref(false)

// 获取角色列表
const getRoleList = async () => {
  loading.value = true
  try {
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    tableData.value = [
      {
        id: 1,
        name: '超级管理员',
        code: 'admin',
        description: '拥有系统所有权限',
        status: 1,
        createTime: formatDate(new Date())
      },
      {
        id: 2,
        name: '普通用户',
        code: 'user',
        description: '普通用户权限',
        status: 1,
        createTime: formatDate(new Date())
      }
    ]
  } catch (error) {
    ElMessage.error('获取角色列表失败')
  } finally {
    loading.value = false
  }
}

// 新增角色
const handleAdd = () => {
  ElMessage.info('新增角色功能开发中...')
}

// 编辑角色
const handleEdit = (row: any) => {
  ElMessage.info(`编辑角色: ${row.name}`)
}

// 权限设置
const handlePermission = (row: any) => {
  ElMessage.info(`设置角色权限: ${row.name}`)
}

// 删除角色
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除角色 "${row.name}" 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    ElMessage.success('删除成功')
    getRoleList()
  } catch (error) {
    // 用户取消
  }
}

onMounted(() => {
  getRoleList()
})
</script>

<style lang="scss" scoped>
.role-page {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
