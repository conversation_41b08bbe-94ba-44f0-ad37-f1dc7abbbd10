// 通用响应接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
}

// 分页参数
export interface PageParams {
  page: number
  pageSize: number
}

// 分页响应
export interface PageResponse<T = any> {
  list: T[]
  total: number
  page: number
  pageSize: number
}

// 用户信息
export interface UserInfo {
  id: string | number
  username: string
  nickname: string
  avatar?: string
  email?: string
  phone?: string
  roles: string[]
  permissions: string[]
}

// 菜单项
export interface MenuItem {
  id: string | number
  title: string
  path: string
  icon?: string
  component?: string
  redirect?: string
  children?: MenuItem[]
  meta?: {
    title: string
    icon?: string
    hidden?: boolean
    keepAlive?: boolean
    requireAuth?: boolean
    roles?: string[]
  }
}

// 表格列配置
export interface TableColumn {
  prop: string
  label: string
  width?: string | number
  minWidth?: string | number
  fixed?: boolean | 'left' | 'right'
  sortable?: boolean
  formatter?: (row: any, column: any, cellValue: any) => string
  type?: 'selection' | 'index' | 'expand' | 'slot'
}

// 表单项配置
export interface FormItem {
  prop: string
  label: string
  type: 'input' | 'select' | 'date' | 'datetime' | 'daterange' | 'textarea' | 'number' | 'switch'
  placeholder?: string
  options?: Array<{ label: string; value: any }>
  rules?: any[]
  span?: number
  disabled?: boolean
  min?: number
  max?: number
}

// 系统菜单数据
export interface MenuData {
  id: number
  title: string
  path: string
  icon: string
  component: string
  sort: number
  status: number
  children: MenuData[]
}

// 角色数据
export interface RoleData {
  id: number
  name: string
  code: string
  description: string
  status: number
  createTime: string
}
