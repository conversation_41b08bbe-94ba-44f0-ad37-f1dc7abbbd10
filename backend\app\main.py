"""
TrainAI Backend - FastAPI应用主入口

这是TrainAI全栈AI训练平台的后端服务主入口文件。
提供RESTful API和WebSocket接口，支持AI模型训练、实验管理等功能。
"""

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import time
import logging
from contextlib import asynccontextmanager

from app.core.config import settings
from app.core.database import connect_to_mongo, close_mongo_connection
from app.api.v1 import api_router


# 配置日志
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format=settings.LOG_FORMAT
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("🚀 TrainAI Backend 正在启动...")
    
    # 连接数据库
    await connect_to_mongo()
    logger.info("✅ 数据库连接成功")
    
    # 这里可以添加其他启动时的初始化操作
    # 例如：创建默认用户、初始化缓存等
    
    logger.info("🎉 TrainAI Backend 启动完成!")
    
    yield
    
    # 关闭时执行
    logger.info("🛑 TrainAI Backend 正在关闭...")
    await close_mongo_connection()
    logger.info("✅ 数据库连接已关闭")


# 创建FastAPI应用实例
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="TrainAI - 全栈AI训练平台后端API",
    docs_url="/docs" if settings.ENABLE_DOCS else None,
    redoc_url="/redoc" if settings.ENABLE_DOCS else None,
    lifespan=lifespan
)


# ================================
# 中间件配置
# ================================

# CORS中间件 - 允许前端跨域访问
if settings.ENABLE_CORS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.CORS_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# 信任主机中间件 - 安全防护
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"] if settings.DEBUG else ["localhost", "127.0.0.1"]
)


# ================================
# 请求处理中间件
# ================================

@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """添加请求处理时间到响应头"""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response


@app.middleware("http")
async def log_requests(request: Request, call_next):
    """记录请求日志"""
    start_time = time.time()
    
    # 记录请求信息
    logger.info(f"📥 {request.method} {request.url.path} - {request.client.host}")
    
    response = await call_next(request)
    
    # 记录响应信息
    process_time = time.time() - start_time
    logger.info(
        f"📤 {request.method} {request.url.path} - "
        f"Status: {response.status_code} - "
        f"Time: {process_time:.3f}s"
    )
    
    return response


# ================================
# 异常处理器
# ================================

@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """HTTP异常处理器"""
    logger.error(f"❌ HTTP异常: {exc.status_code} - {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "error": {
                "code": f"HTTP_{exc.status_code}",
                "message": exc.detail,
                "details": None
            },
            "timestamp": time.time()
        }
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """请求验证异常处理器"""
    logger.error(f"❌ 验证异常: {exc.errors()}")
    return JSONResponse(
        status_code=422,
        content={
            "success": False,
            "error": {
                "code": "VALIDATION_ERROR",
                "message": "请求参数验证失败",
                "details": exc.errors()
            },
            "timestamp": time.time()
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    logger.error(f"💥 未处理异常: {type(exc).__name__}: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": {
                "code": "INTERNAL_ERROR",
                "message": "服务器内部错误" if not settings.DEBUG else str(exc),
                "details": None
            },
            "timestamp": time.time()
        }
    )


# ================================
# 路由配置
# ================================

# 健康检查端点
@app.get("/health", tags=["系统"])
async def health_check():
    """健康检查接口"""
    return {
        "success": True,
        "data": {
            "status": "healthy",
            "app_name": settings.APP_NAME,
            "version": settings.APP_VERSION,
            "environment": settings.ENVIRONMENT
        },
        "message": "服务运行正常",
        "timestamp": time.time()
    }


# 根路径
@app.get("/", tags=["系统"])
async def root():
    """根路径接口"""
    return {
        "success": True,
        "data": {
            "message": f"欢迎使用 {settings.APP_NAME}!",
            "version": settings.APP_VERSION,
            "docs_url": "/docs" if settings.ENABLE_DOCS else None,
            "api_prefix": "/api/v1"
        },
        "message": "TrainAI Backend API",
        "timestamp": time.time()
    }


# 包含API路由
app.include_router(api_router, prefix="/api/v1")


# ================================
# 启动配置
# ================================

if __name__ == "__main__":
    import uvicorn
    
    logger.info(f"🚀 启动 {settings.APP_NAME} 开发服务器...")
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=settings.BACKEND_PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
