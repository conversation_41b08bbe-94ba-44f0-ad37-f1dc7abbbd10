# TrainAI - 全栈AI训练平台

<div align="center">

![TrainAI Logo](https://via.placeholder.com/200x100/4CAF50/FFFFFF?text=TrainAI)

**一个现代化的全栈AI模型训练平台**

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com)
[![React](https://img.shields.io/badge/React-18+-blue.svg)](https://reactjs.org)
[![TypeScript](https://img.shields.io/badge/TypeScript-5+-blue.svg)](https://typescriptlang.org)
[![Docker](https://img.shields.io/badge/Docker-20.10+-blue.svg)](https://docker.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

</div>

## 🚀 项目简介

TrainAI是一个现代化的全栈AI训练平台，旨在简化机器学习和深度学习模型的开发流程。它支持从传统机器学习到最新的生成式AI模型的训练，提供直观的Web界面和强大的API接口。

### ✨ 核心特性

- 🤖 **全栈AI支持**: 传统ML + 深度学习 + 生成式AI
- 🎯 **易于使用**: 直观的Web界面 + 自然语言交互
- 📊 **完整MLOps**: 实验管理、模型版本控制、部署监控
- 🔧 **高度可扩展**: 模块化架构，支持自定义算法
- 🌐 **分布式训练**: 多GPU、多节点训练支持
- 📈 **实时监控**: 训练过程可视化和性能监控
- 🔒 **企业级安全**: 用户权限管理、数据加密

### 🎯 支持的AI技术

#### 传统机器学习
- **算法**: Random Forest, XGBoost, LightGBM, SVM, 逻辑回归
- **应用**: 分类、回归、聚类、降维
- **特点**: 快速训练、高解释性、适合结构化数据

#### 深度学习
- **框架**: PyTorch, TensorFlow
- **模型**: CNN, RNN, LSTM, Transformer
- **应用**: 图像识别、自然语言处理、时序预测

#### 生成式AI
- **模型**: GAN, Diffusion, VAE, GPT
- **应用**: 图像生成、文本生成、风格转换
- **特点**: 创意内容生成、数据增强

## 🏗️ 技术架构

```
┌─────────────────────────────────────────────────────────────┐
│                    用户交互层                                │
├─────────────────┬─────────────────┬─────────────────────────┤
│   Web界面       │   API接口       │   自然语言接口           │
├─────────────────┴─────────────────┴─────────────────────────┤
│                    业务逻辑层                                │
├─────────────────┬─────────────────┬─────────────────────────┤
│   实验管理      │   模型管理      │   任务调度               │
├─────────────────┼─────────────────┼─────────────────────────┤
│   数据管理      │   训练引擎      │   评估引擎               │
├─────────────────┴─────────────────┴─────────────────────────┤
│                    算法引擎层                                │
├─────────────────┬─────────────────┬─────────────────────────┤
│   传统ML        │   深度学习      │   生成式AI               │
│   (sklearn)     │   (PyTorch)     │   (Diffusion/GAN)       │
├─────────────────┴─────────────────┴─────────────────────────┤
│                    基础设施层                                │
├─────────────────┬─────────────────┬─────────────────────────┤
│   数据存储      │   计算资源      │   监控日志               │
│   (MongoDB)     │   (GPU/CPU)     │   (Prometheus)          │
└─────────────────┴─────────────────┴─────────────────────────┘
```

### 🛠️ 技术栈

**后端**
- **框架**: FastAPI (高性能异步框架)
- **数据库**: MongoDB (文档数据库) + Redis (缓存)
- **存储**: MinIO (对象存储)
- **任务队列**: Celery + Redis
- **容器化**: Docker + Docker Compose

**前端**
- **框架**: React 18 + TypeScript
- **UI库**: Ant Design
- **状态管理**: Redux Toolkit
- **图表**: ECharts + D3.js
- **构建工具**: Vite

**AI/ML**
- **传统ML**: scikit-learn, XGBoost, LightGBM
- **深度学习**: PyTorch, TorchVision, Transformers
- **生成式AI**: Diffusers, Stable Diffusion
- **数据处理**: Pandas, NumPy, OpenCV

## 🚀 快速开始

### 📋 系统要求

- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+
- **Python**: 3.8+
- **Node.js**: 16+
- **Docker**: 20.10+
- **GPU**: NVIDIA GPU (可选，用于深度学习)

### 🐳 使用Docker快速启动

```bash
# 克隆项目
git clone https://github.com/your-org/TrainAI.git
cd TrainAI

# 启动所有服务
docker-compose -f docker/docker-compose.dev.yml up -d

# 访问应用
# 前端: http://localhost:3000
# 后端API: http://localhost:8000
# API文档: http://localhost:8000/docs
```

### 💻 本地开发环境

#### 后端设置
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 前端设置
```bash
cd frontend
npm install
npm start
```

#### 数据库设置
```bash
# 启动MongoDB
docker run -d -p 27017:27017 --name mongodb mongo:latest

# 启动Redis
docker run -d -p 6379:6379 --name redis redis:latest

# 启动MinIO
docker run -d -p 9000:9000 -p 9001:9001 \
  --name minio \
  -e "MINIO_ROOT_USER=minioadmin" \
  -e "MINIO_ROOT_PASSWORD=minioadmin" \
  minio/minio server /data --console-address ":9001"
```

## 📖 使用示例

### 1. 创建数据集
```python
import requests

# 上传CSV数据集
files = {'file': open('dataset.csv', 'rb')}
data = {
    'name': '销售数据集',
    'description': '包含销售数据的CSV文件'
}
response = requests.post('http://localhost:8000/api/v1/datasets', 
                        files=files, data=data)
```

### 2. 创建实验
```python
experiment_config = {
    "name": "销售预测实验",
    "dataset_id": "dataset_123",
    "model_type": "sklearn",
    "algorithm": "random_forest",
    "config": {
        "model_params": {
            "n_estimators": 100,
            "max_depth": 10
        },
        "training_params": {
            "test_size": 0.2,
            "random_state": 42
        }
    }
}

response = requests.post('http://localhost:8000/api/v1/experiments',
                        json=experiment_config)
```

### 3. 开始训练
```python
training_request = {
    "experiment_id": "exp_456"
}

response = requests.post('http://localhost:8000/api/v1/training/start',
                        json=training_request)
```

### 4. 监控训练进度
```javascript
// 使用WebSocket实时监控
const ws = new WebSocket('ws://localhost:8000/ws/training/exp_456');

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('训练进度:', data.progress);
    console.log('当前指标:', data.metrics);
};
```

## 📚 文档

- [📋 技术架构文档](docs/技术架构文档.md) - 详细的技术架构和设计
- [🔌 API文档](docs/API文档.md) - 完整的API接口文档
- [👨‍💻 开发指南](docs/开发指南.md) - 开发环境搭建和规范
- [📖 用户手册](docs/用户手册.md) - 用户使用指南

## 🗺️ 开发路线图

### 🎯 第一阶段 (基础平台) - 4周
- [x] 项目架构设计
- [ ] 基础框架搭建 (FastAPI + React)
- [ ] 数据管理基础功能
- [ ] 传统ML训练支持 (sklearn)
- [ ] 基础实验管理

### 🎯 第二阶段 (深度学习) - 6周
- [ ] PyTorch训练引擎
- [ ] CNN/RNN模型支持
- [ ] GPU训练支持
- [ ] 模型可视化和监控

### 🎯 第三阶段 (高级功能) - 8周
- [ ] Transformer模型支持
- [ ] 生成式AI (GAN/Diffusion)
- [ ] 自然语言接口
- [ ] 分布式训练

### 🎯 第四阶段 (优化完善) - 4周
- [ ] 性能优化
- [ ] 用户体验优化
- [ ] 文档完善
- [ ] 测试覆盖

## 🤝 贡献指南

我们欢迎所有形式的贡献！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解详细信息。

### 🐛 报告问题
如果您发现了bug或有功能建议，请在 [Issues](https://github.com/your-org/TrainAI/issues) 中提交。

### 💡 提交代码
1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [FastAPI](https://fastapi.tiangolo.com/) - 现代化的Python Web框架
- [React](https://reactjs.org/) - 用户界面构建库
- [PyTorch](https://pytorch.org/) - 深度学习框架
- [scikit-learn](https://scikit-learn.org/) - 机器学习库
- [Ant Design](https://ant.design/) - 企业级UI设计语言

## 📞 联系我们

- **项目主页**: https://github.com/your-org/TrainAI
- **文档**: https://trainai-docs.example.com
- **邮箱**: <EMAIL>
- **微信群**: 扫描二维码加入讨论群

---

<div align="center">
  <p>如果这个项目对您有帮助，请给我们一个 ⭐️</p>
  <p>Made with ❤️ by TrainAI Team</p>
</div>
