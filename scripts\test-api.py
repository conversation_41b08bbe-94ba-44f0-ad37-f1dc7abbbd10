#!/usr/bin/env python3
"""
TrainAI API 测试脚本

测试后端API的基本功能是否正常工作。
"""

import requests
import json
import time
import sys
from typing import Dict, Any


class TrainAIAPITester:
    """TrainAI API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.access_token = None
    
    def test_health_check(self) -> bool:
        """测试健康检查接口"""
        print("🔍 测试健康检查接口...")
        try:
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 健康检查通过: {data.get('data', {}).get('status')}")
                return True
            else:
                print(f"❌ 健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")
            return False
    
    def test_root_endpoint(self) -> bool:
        """测试根路径接口"""
        print("🔍 测试根路径接口...")
        try:
            response = self.session.get(f"{self.base_url}/")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 根路径访问成功: {data.get('data', {}).get('message')}")
                return True
            else:
                print(f"❌ 根路径访问失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 根路径访问异常: {e}")
            return False
    
    def test_user_registration(self) -> bool:
        """测试用户注册"""
        print("🔍 测试用户注册...")
        try:
            test_user = {
                "username": f"testuser_{int(time.time())}",
                "email": f"test_{int(time.time())}@example.com",
                "password": "TestPassword123!",
                "full_name": "Test User"
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/auth/register",
                json=test_user
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 用户注册成功: {data.get('data', {}).get('username')}")
                return True
            else:
                print(f"❌ 用户注册失败: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ 用户注册异常: {e}")
            return False
    
    def test_user_login(self) -> bool:
        """测试用户登录"""
        print("🔍 测试用户登录...")
        try:
            login_data = {
                "username": "admin",
                "password": "admin123"
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/auth/login",
                json=login_data
            )
            
            if response.status_code == 200:
                data = response.json()
                self.access_token = data.get("access_token")
                self.session.headers.update({
                    "Authorization": f"Bearer {self.access_token}"
                })
                print(f"✅ 用户登录成功: {data.get('user', {}).get('username')}")
                return True
            else:
                print(f"❌ 用户登录失败: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ 用户登录异常: {e}")
            return False
    
    def test_get_current_user(self) -> bool:
        """测试获取当前用户信息"""
        print("🔍 测试获取当前用户信息...")
        if not self.access_token:
            print("❌ 需要先登录")
            return False
        
        try:
            response = self.session.get(f"{self.base_url}/api/v1/auth/me")
            
            if response.status_code == 200:
                data = response.json()
                user_info = data.get('data', {})
                print(f"✅ 获取用户信息成功: {user_info.get('username')}")
                return True
            else:
                print(f"❌ 获取用户信息失败: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ 获取用户信息异常: {e}")
            return False
    
    def test_datasets_endpoint(self) -> bool:
        """测试数据集接口"""
        print("🔍 测试数据集接口...")
        if not self.access_token:
            print("❌ 需要先登录")
            return False
        
        try:
            response = self.session.get(f"{self.base_url}/api/v1/datasets/")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 数据集接口访问成功: 共{data.get('data', {}).get('total', 0)}个数据集")
                return True
            else:
                print(f"❌ 数据集接口访问失败: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ 数据集接口访问异常: {e}")
            return False
    
    def test_experiments_endpoint(self) -> bool:
        """测试实验接口"""
        print("🔍 测试实验接口...")
        if not self.access_token:
            print("❌ 需要先登录")
            return False
        
        try:
            response = self.session.get(f"{self.base_url}/api/v1/experiments/")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 实验接口访问成功: 共{data.get('data', {}).get('total', 0)}个实验")
                return True
            else:
                print(f"❌ 实验接口访问失败: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ 实验接口访问异常: {e}")
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """运行所有测试"""
        print("🚀 开始 TrainAI API 测试...\n")
        
        tests = [
            ("健康检查", self.test_health_check),
            ("根路径", self.test_root_endpoint),
            ("用户注册", self.test_user_registration),
            ("用户登录", self.test_user_login),
            ("获取用户信息", self.test_get_current_user),
            ("数据集接口", self.test_datasets_endpoint),
            ("实验接口", self.test_experiments_endpoint),
        ]
        
        results = {}
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                result = test_func()
                results[test_name] = result
                if result:
                    passed += 1
                print()  # 空行分隔
            except Exception as e:
                print(f"❌ 测试 {test_name} 发生异常: {e}")
                results[test_name] = False
                print()
        
        # 输出测试结果摘要
        print("=" * 50)
        print("📊 测试结果摘要:")
        print(f"   总测试数: {total}")
        print(f"   通过数量: {passed}")
        print(f"   失败数量: {total - passed}")
        print(f"   通过率: {passed/total*100:.1f}%")
        print("=" * 50)
        
        # 详细结果
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
        
        return results


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="TrainAI API 测试脚本")
    parser.add_argument(
        "--url", 
        default="http://localhost:8000",
        help="API服务器地址 (默认: http://localhost:8000)"
    )
    parser.add_argument(
        "--wait",
        type=int,
        default=0,
        help="测试前等待时间（秒）"
    )
    
    args = parser.parse_args()
    
    if args.wait > 0:
        print(f"⏳ 等待 {args.wait} 秒后开始测试...")
        time.sleep(args.wait)
    
    # 创建测试器并运行测试
    tester = TrainAIAPITester(args.url)
    results = tester.run_all_tests()
    
    # 根据测试结果设置退出码
    failed_tests = [name for name, result in results.items() if not result]
    if failed_tests:
        print(f"\n❌ 有 {len(failed_tests)} 个测试失败")
        sys.exit(1)
    else:
        print("\n🎉 所有测试通过！")
        sys.exit(0)


if __name__ == "__main__":
    main()
