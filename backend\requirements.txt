# TrainAI Backend Dependencies

# ================================
# Web框架
# ================================
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# ================================
# 数据库和存储
# ================================
# MongoDB
motor==3.3.2
pymongo==4.6.0

# Redis
redis==5.0.1
aioredis==2.0.1

# MinIO (对象存储)
minio==7.2.0

# ================================
# 认证和安全
# ================================
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
bcrypt==4.1.2

# ================================
# 数据处理和验证
# ================================
pydantic==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0

# ================================
# 传统机器学习
# ================================
scikit-learn==1.3.2
xgboost==2.0.2
lightgbm==4.1.0
catboost==1.2.2

# 数据处理
pandas==2.1.4
numpy==1.24.4
scipy==1.11.4

# ================================
# 深度学习
# ================================
# PyTorch (CPU版本，GPU版本需要根据CUDA版本安装)
torch==2.1.1
torchvision==0.16.1
torchaudio==2.1.1

# Transformers
transformers==4.36.2
tokenizers==0.15.0
datasets==2.15.0

# 生成式AI
diffusers==0.24.0
accelerate==0.25.0

# ================================
# 图像和视觉处理
# ================================
opencv-python==********
Pillow==10.1.0
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0

# ================================
# 自然语言处理
# ================================
nltk==3.8.1
spacy==3.7.2
textblob==0.17.1

# ================================
# 任务队列和异步
# ================================
celery==5.3.4
flower==2.0.1

# ================================
# 监控和日志
# ================================
prometheus-client==0.19.0
structlog==23.2.0
loguru==0.7.2

# ================================
# 文件处理
# ================================
openpyxl==3.1.2
pyarrow==14.0.1
h5py==3.10.0

# ================================
# 超参数优化
# ================================
optuna==3.4.0
hyperopt==0.2.7

# ================================
# 模型解释和可视化
# ================================
shap==0.43.0
lime==0.2.0.1
yellowbrick==1.5

# ================================
# API文档和测试
# ================================
httpx==0.25.2
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0

# ================================
# 开发工具
# ================================
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# ================================
# 其他工具
# ================================
python-dotenv==1.0.0
click==8.1.7
tqdm==4.66.1
requests==2.31.0
aiofiles==23.2.1
jinja2==3.1.2

# ================================
# CORS和中间件
# ================================
python-cors==1.7.0

# ================================
# 时间处理
# ================================
python-dateutil==2.8.2
pytz==2023.3

# ================================
# 配置管理
# ================================
pyyaml==6.0.1
toml==0.10.2

# ================================
# 数据验证和序列化
# ================================
marshmallow==3.20.1
cerberus==1.3.5
