"""
TrainAI API v1 路由配置

包含所有API v1版本的路由定义。
"""

from fastapi import APIRouter

from app.api.v1 import auth, datasets, experiments, models, training

# 创建API v1路由器
api_router = APIRouter()

# 包含各个模块的路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(datasets.router, prefix="/datasets", tags=["数据集"])
api_router.include_router(experiments.router, prefix="/experiments", tags=["实验"])
api_router.include_router(models.router, prefix="/models", tags=["模型"])
api_router.include_router(training.router, prefix="/training", tags=["训练"])
