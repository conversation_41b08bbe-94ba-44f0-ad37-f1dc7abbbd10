#!/usr/bin/env python3
"""
修复Colab脚本中的魔法命令问题

将Jupyter/Colab的魔法命令转换为标准Python代码
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command, cwd=None, check=True):
    """
    执行shell命令
    
    Args:
        command (str): 要执行的命令
        cwd (str): 工作目录
        check (bool): 是否检查返回码
    
    Returns:
        subprocess.CompletedProcess: 执行结果
    """
    try:
        print(f"🔧 执行命令: {command}")
        
        # 分割命令
        if isinstance(command, str):
            # 简单的命令分割，对于复杂命令可能需要使用shlex.split()
            cmd_parts = command.split()
        else:
            cmd_parts = command
        
        # 执行命令
        result = subprocess.run(
            cmd_parts,
            cwd=cwd,
            check=check,
            capture_output=True,
            text=True
        )
        
        # 打印输出
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print(f"⚠️ 警告: {result.stderr}")
        
        return result
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 命令执行失败: {e}")
        print(f"返回码: {e.returncode}")
        if e.stdout:
            print(f"标准输出: {e.stdout}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        raise
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        raise


def fix_colab_script(input_file, output_file=None):
    """
    修复Colab脚本中的魔法命令
    
    Args:
        input_file (str): 输入文件路径
        output_file (str): 输出文件路径，如果为None则覆盖原文件
    """
    if output_file is None:
        output_file = input_file + ".fixed"
    
    print(f"🔧 修复脚本: {input_file} -> {output_file}")
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        fixed_lines = []
        
        for line_num, line in enumerate(lines, 1):
            stripped_line = line.strip()
            
            # 检查是否是魔法命令
            if stripped_line.startswith('!'):
                # 移除开头的!
                command = stripped_line[1:].strip()
                
                # 转换为subprocess调用
                fixed_line = f'run_command("{command}")'
                
                # 保持原有的缩进
                indent = len(line) - len(line.lstrip())
                fixed_line = ' ' * indent + fixed_line
                
                print(f"第{line_num}行: {stripped_line} -> {fixed_line.strip()}")
                fixed_lines.append(fixed_line)
            else:
                fixed_lines.append(line)
        
        # 在文件开头添加必要的导入
        imports = [
            "import subprocess",
            "import sys",
            "import os",
            "",
            "def run_command(command, cwd=None, check=True):",
            '    """执行shell命令"""',
            "    try:",
            '        print(f"🔧 执行命令: {command}")',
            "        cmd_parts = command.split()",
            "        result = subprocess.run(",
            "            cmd_parts,",
            "            cwd=cwd,",
            "            check=check,",
            "            capture_output=True,",
            "            text=True",
            "        )",
            "        if result.stdout:",
            "            print(result.stdout)",
            "        if result.stderr:",
            '            print(f"⚠️ 警告: {result.stderr}")',
            "        return result",
            "    except subprocess.CalledProcessError as e:",
            '        print(f"❌ 命令执行失败: {e}")',
            "        raise",
            "    except Exception as e:",
            '        print(f"❌ 执行异常: {e}")',
            "        raise",
            "",
            "# 原始代码开始",
            ""
        ]
        
        # 合并导入和修复后的代码
        final_content = '\n'.join(imports + fixed_lines)
        
        # 写入修复后的文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(final_content)
        
        print(f"✅ 脚本修复完成: {output_file}")
        return output_file
        
    except Exception as e:
        print(f"❌ 修复脚本失败: {e}")
        raise


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="修复Colab脚本中的魔法命令")
    parser.add_argument("input_file", help="输入的Python脚本文件")
    parser.add_argument("-o", "--output", help="输出文件路径（默认为输入文件名+.fixed）")
    parser.add_argument("--run", action="store_true", help="修复后立即运行脚本")
    
    args = parser.parse_args()
    
    # 检查输入文件是否存在
    if not os.path.exists(args.input_file):
        print(f"❌ 输入文件不存在: {args.input_file}")
        sys.exit(1)
    
    try:
        # 修复脚本
        output_file = fix_colab_script(args.input_file, args.output)
        
        # 如果指定了--run参数，则运行修复后的脚本
        if args.run:
            print(f"\n🚀 运行修复后的脚本: {output_file}")
            run_command(f"python {output_file}")
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
