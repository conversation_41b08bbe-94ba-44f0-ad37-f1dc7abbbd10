# TrainAI API 文档

## 1. API 概述

### 1.1 基础信息
- **Base URL**: `http://localhost:8000/api/v1`
- **认证方式**: JWT Bearer <PERSON>ken
- **数据格式**: JSON
- **字符编码**: UTF-8

### 1.2 通用响应格式
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "code": 200,
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 1.3 错误响应格式
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "参数验证失败",
    "details": {
      "field": "dataset_id",
      "reason": "必填字段"
    }
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 2. 认证接口

### 2.1 用户登录
```http
POST /auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "password123"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "bearer",
    "expires_in": 1800,
    "user": {
      "id": "user_123",
      "username": "admin",
      "email": "<EMAIL>"
    }
  }
}
```

### 2.2 刷新Token
```http
POST /auth/refresh
Authorization: Bearer <access_token>
```

## 3. 数据集管理

### 3.1 获取数据集列表
```http
GET /datasets?page=1&size=10&search=图像
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "dataset_123",
        "name": "CIFAR-10图像分类数据集",
        "description": "包含10个类别的32x32彩色图像",
        "format": "images",
        "size": 163840000,
        "rows": 60000,
        "columns": null,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10,
    "pages": 1
  }
}
```

### 3.2 上传数据集
```http
POST /datasets
Authorization: Bearer <access_token>
Content-Type: multipart/form-data

name: "我的数据集"
description: "数据集描述"
file: <binary_data>
format: "csv"
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "dataset_456",
    "name": "我的数据集",
    "upload_status": "processing",
    "message": "文件上传成功，正在处理中..."
  }
}
```

### 3.3 获取数据集详情
```http
GET /datasets/{dataset_id}
Authorization: Bearer <access_token>
```

### 3.4 数据集预览
```http
GET /datasets/{dataset_id}/preview?rows=100
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "columns": ["feature1", "feature2", "target"],
    "data": [
      [1.2, 3.4, "class_a"],
      [2.1, 4.3, "class_b"]
    ],
    "statistics": {
      "total_rows": 10000,
      "missing_values": 50,
      "data_types": {
        "feature1": "float64",
        "feature2": "float64",
        "target": "object"
      }
    }
  }
}
```

## 4. 实验管理

### 4.1 创建实验
```http
POST /experiments
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "name": "图像分类实验",
  "description": "使用ResNet18进行CIFAR-10分类",
  "dataset_id": "dataset_123",
  "model_type": "pytorch",
  "algorithm": "resnet18",
  "config": {
    "model_config": {
      "num_classes": 10,
      "pretrained": true
    },
    "training_config": {
      "epochs": 50,
      "batch_size": 32,
      "learning_rate": 0.001,
      "optimizer": "adam"
    },
    "data_config": {
      "train_split": 0.8,
      "val_split": 0.2,
      "augmentation": true
    }
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "exp_789",
    "name": "图像分类实验",
    "status": "created",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 4.2 获取实验列表
```http
GET /experiments?status=running&page=1&size=10
Authorization: Bearer <access_token>
```

### 4.3 获取实验详情
```http
GET /experiments/{experiment_id}
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "exp_789",
    "name": "图像分类实验",
    "description": "使用ResNet18进行CIFAR-10分类",
    "status": "completed",
    "progress": 1.0,
    "dataset": {
      "id": "dataset_123",
      "name": "CIFAR-10图像分类数据集"
    },
    "config": {
      "model_type": "pytorch",
      "algorithm": "resnet18"
    },
    "metrics": {
      "final_train_loss": 0.234,
      "final_val_accuracy": 0.892,
      "best_val_accuracy": 0.895,
      "training_time": 3600
    },
    "artifacts": {
      "model_path": "/models/exp_789/model.pth",
      "logs_path": "/logs/exp_789/",
      "plots": [
        "/plots/exp_789/loss_curve.png",
        "/plots/exp_789/accuracy_curve.png"
      ]
    },
    "created_at": "2024-01-01T00:00:00Z",
    "started_at": "2024-01-01T00:05:00Z",
    "completed_at": "2024-01-01T01:05:00Z"
  }
}
```

## 5. 训练管理

### 5.1 开始训练
```http
POST /training/start
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "experiment_id": "exp_789"
}
```

### 5.2 停止训练
```http
POST /training/stop
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "experiment_id": "exp_789"
}
```

### 5.3 获取训练状态
```http
GET /training/{experiment_id}/status
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "experiment_id": "exp_789",
    "status": "running",
    "progress": 0.65,
    "current_epoch": 33,
    "total_epochs": 50,
    "current_metrics": {
      "train_loss": 0.234,
      "val_loss": 0.456,
      "train_accuracy": 0.892,
      "val_accuracy": 0.834
    },
    "estimated_time_remaining": "00:15:30",
    "gpu_usage": {
      "gpu_0": {
        "utilization": 85,
        "memory_used": 6144,
        "memory_total": 8192
      }
    }
  }
}
```

### 5.4 获取训练日志
```http
GET /training/{experiment_id}/logs?lines=100&follow=true
Authorization: Bearer <access_token>
```

## 6. 模型管理

### 6.1 获取模型列表
```http
GET /models?type=pytorch&page=1&size=10
Authorization: Bearer <access_token>
```

### 6.2 获取模型详情
```http
GET /models/{model_id}
Authorization: Bearer <access_token>
```

### 6.3 下载模型
```http
GET /models/{model_id}/download
Authorization: Bearer <access_token>
```

### 6.4 模型推理
```http
POST /models/{model_id}/predict
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "inputs": [
    [1.2, 3.4, 5.6],
    [2.1, 4.3, 6.5]
  ]
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "predictions": [
      {
        "class": "class_a",
        "probability": 0.85,
        "all_probabilities": {
          "class_a": 0.85,
          "class_b": 0.15
        }
      }
    ]
  }
}
```

## 7. WebSocket 接口

### 7.1 训练进度监控
```javascript
// 连接WebSocket
const ws = new WebSocket('ws://localhost:8000/ws/training/exp_789');

// 接收消息
ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('训练进度:', data);
};

// 消息格式
{
  "type": "training_progress",
  "data": {
    "epoch": 10,
    "progress": 0.2,
    "metrics": {
      "loss": 0.456,
      "accuracy": 0.789
    }
  }
}
```

### 7.2 实时日志
```javascript
const ws = new WebSocket('ws://localhost:8000/ws/logs/exp_789');

// 日志消息格式
{
  "type": "log",
  "data": {
    "timestamp": "2024-01-01T00:00:00Z",
    "level": "INFO",
    "message": "Epoch 10/50 - Loss: 0.456"
  }
}
```

## 8. 错误代码

| 错误代码 | HTTP状态码 | 描述 |
|---------|-----------|------|
| VALIDATION_ERROR | 400 | 请求参数验证失败 |
| UNAUTHORIZED | 401 | 未授权访问 |
| FORBIDDEN | 403 | 权限不足 |
| NOT_FOUND | 404 | 资源不存在 |
| CONFLICT | 409 | 资源冲突 |
| TRAINING_IN_PROGRESS | 409 | 训练正在进行中 |
| INSUFFICIENT_RESOURCES | 503 | 计算资源不足 |
| INTERNAL_ERROR | 500 | 服务器内部错误 |
