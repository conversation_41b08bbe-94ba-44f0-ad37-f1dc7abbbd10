<template>
  <div class="menu-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>菜单管理</span>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增菜单
          </el-button>
        </div>
      </template>
      
      <el-table
        :data="menuData"
        v-loading="loading"
        row-key="id"
        border
        default-expand-all
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column prop="title" label="菜单名称" min-width="200" />
        <el-table-column prop="path" label="路由路径" min-width="200" />
        <el-table-column prop="icon" label="图标" width="100">
          <template #default="{ row }">
            <el-icon v-if="row.icon">
              <component :is="row.icon" />
            </el-icon>
          </template>
        </el-table-column>
        <el-table-column prop="component" label="组件路径" min-width="200" />
        <el-table-column prop="sort" label="排序" width="80" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button type="success" size="small" @click="handleAddChild(row)">新增子菜单</el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { MenuData } from '@/types'

const menuData = ref<MenuData[]>([])
const loading = ref(false)

// 获取菜单列表
const getMenuList = async () => {
  loading.value = true
  try {
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟菜单数据
    menuData.value = [
      {
        id: 1,
        title: '仪表盘',
        path: '/dashboard',
        icon: 'Dashboard',
        component: 'dashboard/index',
        sort: 1,
        status: 1,
        children: []
      },
      {
        id: 2,
        title: '用户管理',
        path: '/user',
        icon: 'User',
        component: 'user/index',
        sort: 2,
        status: 1,
        children: []
      },
      {
        id: 3,
        title: '系统管理',
        path: '/system',
        icon: 'Setting',
        component: '',
        sort: 3,
        status: 1,
        children: [
          {
            id: 31,
            title: '菜单管理',
            path: '/system/menu',
            icon: 'Menu',
            component: 'system/menu/index',
            sort: 1,
            status: 1,
            children: []
          },
          {
            id: 32,
            title: '角色管理',
            path: '/system/role',
            icon: 'UserFilled',
            component: 'system/role/index',
            sort: 2,
            status: 1,
            children: []
          }
        ]
      }
    ]
  } catch (error) {
    ElMessage.error('获取菜单列表失败')
  } finally {
    loading.value = false
  }
}

// 新增菜单
const handleAdd = () => {
  ElMessage.info('新增菜单功能开发中...')
}

// 编辑菜单
const handleEdit = (row: any) => {
  ElMessage.info(`编辑菜单: ${row.title}`)
}

// 新增子菜单
const handleAddChild = (row: any) => {
  ElMessage.info(`为 "${row.title}" 新增子菜单`)
}

// 删除菜单
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除菜单 "${row.title}" 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    ElMessage.success('删除成功')
    getMenuList()
  } catch (error) {
    // 用户取消
  }
}

onMounted(() => {
  getMenuList()
})
</script>

<style lang="scss" scoped>
.menu-page {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
