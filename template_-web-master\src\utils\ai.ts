import type { AIMessage } from '@/types/ai'

/**
 * AI相关工具函数
 */

// 生成唯一ID
export const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 格式化消息内容（支持Markdown）
export const formatMessageContent = (content: string): string => {
  if (!content) return ''
  
  return content
    // 代码块
    .replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre class="code-block"><code class="language-$1">$2</code></pre>')
    // 行内代码
    .replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>')
    // 粗体
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // 斜体
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    // 链接
    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')
    // 换行
    .replace(/\n/g, '<br>')
}

// 计算消息token数量（简单估算）
export const estimateTokens = (text: string): number => {
  // 简单的token估算：中文字符按2个token计算，英文单词按1个token计算
  const chineseChars = (text.match(/[\u4e00-\u9fa5]/g) || []).length
  const englishWords = text.replace(/[\u4e00-\u9fa5]/g, '').split(/\s+/).filter(word => word.length > 0).length
  
  return chineseChars * 2 + englishWords
}

// 截断消息以适应token限制
export const truncateMessages = (messages: AIMessage[], maxTokens: number): AIMessage[] => {
  const result: AIMessage[] = []
  let totalTokens = 0
  
  // 从最新消息开始计算
  for (let i = messages.length - 1; i >= 0; i--) {
    const message = messages[i]
    const messageTokens = estimateTokens(message.content)
    
    if (totalTokens + messageTokens <= maxTokens) {
      result.unshift(message)
      totalTokens += messageTokens
    } else {
      break
    }
  }
  
  return result
}

// 生成对话标题
export const generateConversationTitle = (firstMessage: string): string => {
  if (!firstMessage) return '新对话'
  
  // 取前20个字符作为标题
  let title = firstMessage.trim().substring(0, 20)
  
  // 如果被截断，添加省略号
  if (firstMessage.length > 20) {
    title += '...'
  }
  
  // 移除换行符
  title = title.replace(/\n/g, ' ')
  
  return title || '新对话'
}

// 检查消息是否包含敏感内容（简单实现）
export const containsSensitiveContent = (content: string): boolean => {
  const sensitiveWords = [
    // 可以根据需要添加敏感词
    '暴力', '色情', '政治敏感'
  ]
  
  return sensitiveWords.some(word => content.includes(word))
}

// 格式化时间显示
export const formatMessageTime = (timestamp: number): string => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 1天内
    return `${Math.floor(diff / 3600000)}小时前`
  } else if (diff < 604800000) { // 1周内
    return `${Math.floor(diff / 86400000)}天前`
  } else {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }
}

// 解析流式响应
export const parseStreamResponse = (chunk: string): string | null => {
  try {
    const lines = chunk.split('\n').filter(line => line.trim())
    
    for (const line of lines) {
      if (line.startsWith('data: ')) {
        const data = line.slice(6)
        
        if (data === '[DONE]') {
          return null
        }
        
        const parsed = JSON.parse(data)
        const content = parsed.choices?.[0]?.delta?.content
        
        if (content) {
          return content
        }
      }
    }
  } catch (error) {
    console.warn('解析流数据失败:', error)
  }
  
  return null
}

// 验证AI配置
export const validateAIConfig = (config: any): { valid: boolean; errors: string[] } => {
  const errors: string[] = []
  
  if (!config.baseUrl) {
    errors.push('API地址不能为空')
  } else if (!/^https?:\/\/.+/.test(config.baseUrl)) {
    errors.push('API地址格式不正确')
  }
  
  if (!config.model) {
    errors.push('模型不能为空')
  }
  
  if (config.maxTokens && (config.maxTokens < 100 || config.maxTokens > 8000)) {
    errors.push('最大令牌数应在100-8000之间')
  }
  
  if (config.temperature && (config.temperature < 0 || config.temperature > 2)) {
    errors.push('温度值应在0-2之间')
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

// 导出默认AI提供商配置
export const defaultAIProviders = [
  {
    id: 'openai',
    name: 'OpenAI',
    baseUrl: 'https://api.openai.com/v1',
    requiresApiKey: true,
    models: [
      {
        id: 'gpt-3.5-turbo',
        name: 'GPT-3.5 Turbo',
        description: '快速、经济的对话模型',
        maxTokens: 4096,
        supportStream: true
      },
      {
        id: 'gpt-4',
        name: 'GPT-4',
        description: '更强大的推理能力',
        maxTokens: 8192,
        supportStream: true
      },
      {
        id: 'gpt-4-turbo',
        name: 'GPT-4 Turbo',
        description: '最新的GPT-4模型',
        maxTokens: 128000,
        supportStream: true
      }
    ]
  },
  {
    id: 'azure-openai',
    name: 'Azure OpenAI',
    baseUrl: 'https://your-resource.openai.azure.com/openai/deployments',
    requiresApiKey: true,
    models: [
      {
        id: 'gpt-35-turbo',
        name: 'GPT-3.5 Turbo',
        description: 'Azure部署的GPT-3.5',
        maxTokens: 4096,
        supportStream: true
      }
    ]
  },
  {
    id: 'custom',
    name: '自定义API',
    baseUrl: '',
    requiresApiKey: true,
    models: [
      {
        id: 'custom-model',
        name: '自定义模型',
        description: '自定义API模型',
        maxTokens: 4096,
        supportStream: true
      }
    ]
  }
]