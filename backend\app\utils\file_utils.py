"""
TrainAI 文件处理工具

提供文件上传、下载、验证等功能。
"""

import os
import hashlib
import mimetypes
from pathlib import Path
from typing import Optional, List, Tuple
import aiofiles
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)


def get_file_extension(filename: str) -> str:
    """获取文件扩展名"""
    return Path(filename).suffix.lower().lstrip('.')


def is_allowed_file(filename: str) -> bool:
    """检查文件扩展名是否被允许"""
    extension = get_file_extension(filename)
    return extension in settings.ALLOWED_EXTENSIONS


def get_file_size(file_path: str) -> int:
    """获取文件大小（字节）"""
    try:
        return os.path.getsize(file_path)
    except OSError:
        return 0


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小显示"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"


def get_file_hash(file_path: str, algorithm: str = "md5") -> str:
    """计算文件哈希值"""
    hash_func = hashlib.new(algorithm)
    
    try:
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_func.update(chunk)
        return hash_func.hexdigest()
    except Exception as e:
        logger.error(f"计算文件哈希失败: {e}")
        return ""


def get_mime_type(file_path: str) -> str:
    """获取文件MIME类型"""
    mime_type, _ = mimetypes.guess_type(file_path)
    return mime_type or "application/octet-stream"


def create_directory(directory: str) -> bool:
    """创建目录"""
    try:
        Path(directory).mkdir(parents=True, exist_ok=True)
        return True
    except Exception as e:
        logger.error(f"创建目录失败: {e}")
        return False


def safe_filename(filename: str) -> str:
    """生成安全的文件名"""
    # 移除或替换不安全的字符
    unsafe_chars = '<>:"/\\|?*'
    safe_name = filename
    
    for char in unsafe_chars:
        safe_name = safe_name.replace(char, '_')
    
    # 限制文件名长度
    if len(safe_name) > 255:
        name, ext = os.path.splitext(safe_name)
        safe_name = name[:255-len(ext)] + ext
    
    return safe_name


def generate_unique_filename(original_filename: str, directory: str) -> str:
    """生成唯一的文件名"""
    safe_name = safe_filename(original_filename)
    name, ext = os.path.splitext(safe_name)
    
    counter = 1
    unique_name = safe_name
    
    while os.path.exists(os.path.join(directory, unique_name)):
        unique_name = f"{name}_{counter}{ext}"
        counter += 1
    
    return unique_name


async def save_upload_file(upload_file, destination: str) -> Tuple[bool, str]:
    """保存上传的文件"""
    try:
        # 创建目标目录
        os.makedirs(os.path.dirname(destination), exist_ok=True)
        
        # 异步保存文件
        async with aiofiles.open(destination, 'wb') as f:
            content = await upload_file.read()
            await f.write(content)
        
        logger.info(f"文件保存成功: {destination}")
        return True, "文件保存成功"
        
    except Exception as e:
        error_msg = f"文件保存失败: {e}"
        logger.error(error_msg)
        return False, error_msg


def validate_file_size(file_size: int) -> Tuple[bool, str]:
    """验证文件大小"""
    if file_size <= 0:
        return False, "文件大小无效"
    
    if file_size > settings.MAX_FILE_SIZE:
        max_size_str = format_file_size(settings.MAX_FILE_SIZE)
        current_size_str = format_file_size(file_size)
        return False, f"文件大小超出限制。最大允许: {max_size_str}，当前: {current_size_str}"
    
    return True, "文件大小验证通过"


def get_file_info(file_path: str) -> dict:
    """获取文件详细信息"""
    try:
        stat = os.stat(file_path)
        return {
            "path": file_path,
            "name": os.path.basename(file_path),
            "size": stat.st_size,
            "size_formatted": format_file_size(stat.st_size),
            "extension": get_file_extension(file_path),
            "mime_type": get_mime_type(file_path),
            "created_time": stat.st_ctime,
            "modified_time": stat.st_mtime,
            "hash_md5": get_file_hash(file_path, "md5"),
            "exists": True
        }
    except Exception as e:
        logger.error(f"获取文件信息失败: {e}")
        return {
            "path": file_path,
            "exists": False,
            "error": str(e)
        }


def cleanup_temp_files(directory: str, max_age_hours: int = 24) -> int:
    """清理临时文件"""
    import time
    
    cleaned_count = 0
    max_age_seconds = max_age_hours * 3600
    current_time = time.time()
    
    try:
        for file_path in Path(directory).rglob("*"):
            if file_path.is_file():
                file_age = current_time - file_path.stat().st_mtime
                if file_age > max_age_seconds:
                    try:
                        file_path.unlink()
                        cleaned_count += 1
                        logger.info(f"清理临时文件: {file_path}")
                    except Exception as e:
                        logger.error(f"清理文件失败 {file_path}: {e}")
    
    except Exception as e:
        logger.error(f"清理临时文件目录失败: {e}")
    
    return cleaned_count


def get_directory_size(directory: str) -> int:
    """获取目录总大小"""
    total_size = 0
    try:
        for file_path in Path(directory).rglob("*"):
            if file_path.is_file():
                total_size += file_path.stat().st_size
    except Exception as e:
        logger.error(f"计算目录大小失败: {e}")
    
    return total_size


def list_files_in_directory(
    directory: str, 
    extensions: Optional[List[str]] = None,
    recursive: bool = False
) -> List[dict]:
    """列出目录中的文件"""
    files = []
    
    try:
        pattern = "**/*" if recursive else "*"
        for file_path in Path(directory).glob(pattern):
            if file_path.is_file():
                # 检查扩展名过滤
                if extensions:
                    file_ext = get_file_extension(file_path.name)
                    if file_ext not in extensions:
                        continue
                
                files.append(get_file_info(str(file_path)))
    
    except Exception as e:
        logger.error(f"列出目录文件失败: {e}")
    
    return files


if __name__ == "__main__":
    # 测试文件工具函数
    print("🔧 测试文件工具函数...")
    
    # 测试文件大小格式化
    sizes = [0, 1024, 1048576, 1073741824]
    for size in sizes:
        print(f"{size} bytes = {format_file_size(size)}")
    
    # 测试安全文件名
    unsafe_names = ["test<file>.txt", "file:with|chars.pdf", "very_long_filename_" * 20 + ".txt"]
    for name in unsafe_names:
        safe_name = safe_filename(name)
        print(f"'{name}' -> '{safe_name}'")
    
    # 测试文件扩展名检查
    test_files = ["data.csv", "model.pth", "image.jpg", "script.exe"]
    for filename in test_files:
        allowed = is_allowed_file(filename)
        print(f"'{filename}' allowed: {allowed}")
