"""
TrainAI 认证API路由

提供用户认证、注册、令牌管理等功能。
"""

from datetime import timedelta
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from pydantic import BaseModel, EmailStr
import logging
from typing import Optional
import time

from app.core.config import settings
from app.core.database import get_database
from app.core.security import (
    verify_password, 
    get_password_hash, 
    create_access_token, 
    create_refresh_token,
    verify_token,
    get_current_user,
    validate_password_strength
)

logger = logging.getLogger(__name__)

router = APIRouter()


# ================================
# Pydantic模型
# ================================

class UserRegister(BaseModel):
    """用户注册模型"""
    username: str
    email: EmailStr
    password: str
    full_name: Optional[str] = None


class UserLogin(BaseModel):
    """用户登录模型"""
    username: str
    password: str


class Token(BaseModel):
    """令牌响应模型"""
    access_token: str
    refresh_token: str
    token_type: str
    expires_in: int
    user: dict


class TokenRefresh(BaseModel):
    """令牌刷新模型"""
    refresh_token: str


class PasswordChange(BaseModel):
    """密码修改模型"""
    old_password: str
    new_password: str


class PasswordReset(BaseModel):
    """密码重置模型"""
    email: EmailStr


# ================================
# 认证路由
# ================================

@router.post("/register", response_model=dict, summary="用户注册")
async def register(user_data: UserRegister):
    """
    用户注册接口
    
    - **username**: 用户名（唯一）
    - **email**: 邮箱地址（唯一）
    - **password**: 密码
    - **full_name**: 全名（可选）
    """
    try:
        db = get_database()
        
        # 验证密码强度
        is_valid, message = validate_password_strength(user_data.password)
        if not is_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"密码不符合要求: {message}"
            )
        
        # 检查用户名是否已存在
        existing_user = await db.users.find_one({"username": user_data.username})
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在"
            )
        
        # 检查邮箱是否已存在
        existing_email = await db.users.find_one({"email": user_data.email})
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已被注册"
            )
        
        # 创建新用户
        hashed_password = get_password_hash(user_data.password)
        new_user = {
            "username": user_data.username,
            "email": user_data.email,
            "full_name": user_data.full_name,
            "hashed_password": hashed_password,
            "is_active": True,
            "is_superuser": False,
            "permissions": ["read", "write"],
            "created_at": time.time(),
            "updated_at": time.time()
        }
        
        result = await db.users.insert_one(new_user)
        user_id = str(result.inserted_id)
        
        logger.info(f"新用户注册成功: {user_data.username}")
        
        return {
            "success": True,
            "data": {
                "user_id": user_id,
                "username": user_data.username,
                "email": user_data.email
            },
            "message": "用户注册成功",
            "timestamp": time.time()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户注册失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="注册失败，请稍后重试"
        )


@router.post("/login", response_model=Token, summary="用户登录")
async def login(user_data: UserLogin):
    """
    用户登录接口
    
    - **username**: 用户名
    - **password**: 密码
    
    返回访问令牌和刷新令牌
    """
    try:
        db = get_database()
        
        # 查找用户
        user = await db.users.find_one({"username": user_data.username})
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
        
        # 验证密码
        if not verify_password(user_data.password, user["hashed_password"]):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
        
        # 检查用户状态
        if not user.get("is_active", True):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户账户已被禁用"
            )
        
        # 创建令牌
        user_id = str(user["_id"])
        token_data = {"sub": user_id, "username": user["username"]}
        
        access_token = create_access_token(token_data)
        refresh_token = create_refresh_token(token_data)
        
        # 更新最后登录时间
        await db.users.update_one(
            {"_id": user["_id"]},
            {"$set": {"last_login": time.time()}}
        )
        
        # 准备用户信息（移除敏感数据）
        user_info = {
            "id": user_id,
            "username": user["username"],
            "email": user["email"],
            "full_name": user.get("full_name"),
            "is_superuser": user.get("is_superuser", False),
            "permissions": user.get("permissions", [])
        }
        
        logger.info(f"用户登录成功: {user['username']}")
        
        return Token(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            user=user_info
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户登录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录失败，请稍后重试"
        )


@router.post("/refresh", response_model=dict, summary="刷新令牌")
async def refresh_token(token_data: TokenRefresh):
    """
    刷新访问令牌
    
    - **refresh_token**: 刷新令牌
    """
    try:
        # 验证刷新令牌
        payload = verify_token(token_data.refresh_token, "refresh")
        user_id = payload.get("sub")
        username = payload.get("username")
        
        if not user_id or not username:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的刷新令牌"
            )
        
        # 验证用户是否仍然存在且活跃
        db = get_database()
        from bson import ObjectId
        user = await db.users.find_one({"_id": ObjectId(user_id)})
        
        if not user or not user.get("is_active", True):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在或已被禁用"
            )
        
        # 创建新的访问令牌
        token_data = {"sub": user_id, "username": username}
        new_access_token = create_access_token(token_data)
        
        logger.info(f"令牌刷新成功: {username}")
        
        return {
            "success": True,
            "data": {
                "access_token": new_access_token,
                "token_type": "bearer",
                "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
            },
            "message": "令牌刷新成功",
            "timestamp": time.time()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"令牌刷新失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="令牌刷新失败"
        )


@router.get("/me", response_model=dict, summary="获取当前用户信息")
async def get_current_user_info(current_user: dict = Depends(get_current_user)):
    """
    获取当前登录用户的信息
    """
    return {
        "success": True,
        "data": current_user,
        "message": "获取用户信息成功",
        "timestamp": time.time()
    }


@router.post("/change-password", response_model=dict, summary="修改密码")
async def change_password(
    password_data: PasswordChange,
    current_user: dict = Depends(get_current_user)
):
    """
    修改当前用户密码
    
    - **old_password**: 旧密码
    - **new_password**: 新密码
    """
    try:
        db = get_database()
        from bson import ObjectId
        
        # 获取用户完整信息
        user = await db.users.find_one({"_id": ObjectId(current_user["id"])})
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 验证旧密码
        if not verify_password(password_data.old_password, user["hashed_password"]):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="旧密码错误"
            )
        
        # 验证新密码强度
        is_valid, message = validate_password_strength(password_data.new_password)
        if not is_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"新密码不符合要求: {message}"
            )
        
        # 更新密码
        new_hashed_password = get_password_hash(password_data.new_password)
        await db.users.update_one(
            {"_id": ObjectId(current_user["id"])},
            {
                "$set": {
                    "hashed_password": new_hashed_password,
                    "updated_at": time.time()
                }
            }
        )
        
        logger.info(f"用户密码修改成功: {current_user['username']}")
        
        return {
            "success": True,
            "data": None,
            "message": "密码修改成功",
            "timestamp": time.time()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"密码修改失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="密码修改失败"
        )


@router.post("/logout", response_model=dict, summary="用户登出")
async def logout(current_user: dict = Depends(get_current_user)):
    """
    用户登出接口
    
    注意：由于JWT是无状态的，实际的令牌失效需要在客户端处理
    """
    logger.info(f"用户登出: {current_user['username']}")
    
    return {
        "success": True,
        "data": None,
        "message": "登出成功",
        "timestamp": time.time()
    }
