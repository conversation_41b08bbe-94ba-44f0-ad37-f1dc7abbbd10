#!/usr/bin/env python3
"""
Colab到Python脚本转换工具

将包含Jupyter/Colab魔法命令的脚本转换为标准Python脚本
"""

import re
import os
import sys
from pathlib import Path


class ColabToPythonConverter:
    """Colab到Python转换器"""
    
    def __init__(self):
        self.magic_commands = {
            '!': self._convert_shell_command,
            '%': self._convert_magic_command,
            '%%': self._convert_cell_magic,
        }
    
    def _convert_shell_command(self, command):
        """转换shell命令"""
        # 移除开头的!
        cmd = command[1:].strip()
        
        # 处理特殊情况
        if cmd.startswith('pip install'):
            return f'subprocess.run([sys.executable, "-m", "pip", "install"] + "{cmd[12:]}".split(), check=True)'
        elif cmd.startswith('python '):
            script_and_args = cmd[7:].strip()
            return f'subprocess.run([sys.executable, "{script_and_args}"], check=True)'
        else:
            # 通用shell命令
            return f'subprocess.run("{cmd}", shell=True, check=True)'
    
    def _convert_magic_command(self, command):
        """转换魔法命令"""
        cmd = command[1:].strip()
        
        if cmd.startswith('cd '):
            path = cmd[3:].strip()
            return f'os.chdir("{path}")'
        elif cmd == 'pwd':
            return 'print(os.getcwd())'
        elif cmd.startswith('ls'):
            args = cmd[2:].strip()
            if args:
                return f'subprocess.run("ls {args}", shell=True)'
            else:
                return 'subprocess.run("ls", shell=True)'
        else:
            # 其他魔法命令转为注释
            return f'# 原魔法命令: {command}'
    
    def _convert_cell_magic(self, command):
        """转换单元格魔法命令"""
        # 单元格魔法命令通常需要特殊处理
        return f'# 原单元格魔法命令: {command}'
    
    def convert_line(self, line):
        """转换单行代码"""
        stripped = line.strip()
        
        # 检查是否是魔法命令
        for prefix, converter in self.magic_commands.items():
            if stripped.startswith(prefix):
                # 保持原有缩进
                indent = len(line) - len(line.lstrip())
                converted = converter(stripped)
                return ' ' * indent + converted
        
        # 不是魔法命令，直接返回
        return line
    
    def convert_file(self, input_file, output_file=None):
        """转换整个文件"""
        if output_file is None:
            output_file = str(Path(input_file).with_suffix('.py'))
        
        print(f"🔄 转换文件: {input_file} -> {output_file}")
        
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 添加必要的导入
            imports = [
                "#!/usr/bin/env python3\n",
                "# 从Colab脚本自动转换\n",
                "\n",
                "import subprocess\n",
                "import sys\n",
                "import os\n",
                "from pathlib import Path\n",
                "\n",
                "# 原始代码开始\n",
                "\n"
            ]
            
            converted_lines = []
            magic_count = 0
            
            for line_num, line in enumerate(lines, 1):
                original_line = line.rstrip()
                converted_line = self.convert_line(line.rstrip())
                
                if converted_line != original_line:
                    magic_count += 1
                    print(f"  第{line_num}行: {original_line.strip()} -> {converted_line.strip()}")
                
                converted_lines.append(converted_line + '\n')
            
            # 写入转换后的文件
            with open(output_file, 'w', encoding='utf-8') as f:
                f.writelines(imports + converted_lines)
            
            print(f"✅ 转换完成: 共转换了 {magic_count} 个魔法命令")
            return output_file
            
        except Exception as e:
            print(f"❌ 转换失败: {e}")
            raise


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="将Colab脚本转换为标准Python脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python colab_to_python.py script.py
  python colab_to_python.py script.py -o converted_script.py
  python colab_to_python.py script.py --run
        """
    )
    
    parser.add_argument("input_file", help="输入的Python脚本文件")
    parser.add_argument("-o", "--output", help="输出文件路径")
    parser.add_argument("--run", action="store_true", help="转换后立即运行脚本")
    parser.add_argument("--backup", action="store_true", help="备份原文件")
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.input_file):
        print(f"❌ 输入文件不存在: {args.input_file}")
        sys.exit(1)
    
    try:
        # 备份原文件
        if args.backup:
            backup_file = args.input_file + ".backup"
            import shutil
            shutil.copy2(args.input_file, backup_file)
            print(f"📋 已备份原文件: {backup_file}")
        
        # 转换文件
        converter = ColabToPythonConverter()
        output_file = converter.convert_file(args.input_file, args.output)
        
        # 运行转换后的脚本
        if args.run:
            print(f"\n🚀 运行转换后的脚本...")
            try:
                result = subprocess.run([sys.executable, output_file], check=True)
                print("✅ 脚本运行成功")
            except subprocess.CalledProcessError as e:
                print(f"❌ 脚本运行失败: {e}")
                sys.exit(1)
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
