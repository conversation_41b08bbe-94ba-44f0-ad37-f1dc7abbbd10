"""
TrainAI 基础数据模型

定义所有数据模型的基础类和通用字段。
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime
import time


class BaseDocument(BaseModel):
    """基础文档模型"""
    
    id: Optional[str] = Field(None, alias="_id")
    created_at: float = Field(default_factory=time.time)
    updated_at: float = Field(default_factory=time.time)
    
    class Config:
        """Pydantic配置"""
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {
            datetime: lambda v: v.timestamp()
        }


class UserDocument(BaseDocument):
    """用户文档模型"""
    
    username: str = Field(..., min_length=3, max_length=50)
    email: str = Field(..., regex=r'^[^@]+@[^@]+\.[^@]+$')
    full_name: Optional[str] = Field(None, max_length=100)
    hashed_password: str
    is_active: bool = Field(default=True)
    is_superuser: bool = Field(default=False)
    permissions: list = Field(default_factory=lambda: ["read", "write"])
    last_login: Optional[float] = None
    
    class Config:
        """配置"""
        schema_extra = {
            "example": {
                "username": "john_doe",
                "email": "<EMAIL>",
                "full_name": "John Doe",
                "is_active": True,
                "is_superuser": False,
                "permissions": ["read", "write"]
            }
        }


class DatasetDocument(BaseDocument):
    """数据集文档模型"""
    
    name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    user_id: str
    file_path: str
    file_size: int = Field(..., gt=0)
    format: str  # csv, json, parquet, images, etc.
    schema: Optional[Dict[str, Any]] = None  # 数据结构信息
    statistics: Optional[Dict[str, Any]] = None  # 统计信息
    rows: Optional[int] = None
    columns: Optional[int] = None
    status: str = Field(default="processing")  # processing, ready, error
    
    class Config:
        """配置"""
        schema_extra = {
            "example": {
                "name": "CIFAR-10数据集",
                "description": "包含10个类别的32x32彩色图像",
                "format": "images",
                "file_size": 163840000,
                "rows": 60000,
                "status": "ready"
            }
        }


class ExperimentDocument(BaseDocument):
    """实验文档模型"""
    
    name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    user_id: str
    dataset_id: str
    model_type: str  # sklearn, pytorch, transformers, etc.
    algorithm: str  # random_forest, resnet18, bert, etc.
    config: Dict[str, Any] = Field(default_factory=dict)
    status: str = Field(default="created")  # created, running, completed, failed, stopped
    progress: float = Field(default=0.0, ge=0.0, le=1.0)
    metrics: Optional[Dict[str, Any]] = None
    artifacts: Optional[Dict[str, str]] = None  # 模型文件、日志等路径
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    error_message: Optional[str] = None
    
    class Config:
        """配置"""
        schema_extra = {
            "example": {
                "name": "图像分类实验",
                "description": "使用ResNet18进行CIFAR-10分类",
                "dataset_id": "dataset_123",
                "model_type": "pytorch",
                "algorithm": "resnet18",
                "config": {
                    "model_params": {
                        "num_classes": 10,
                        "pretrained": True
                    },
                    "training_params": {
                        "epochs": 50,
                        "batch_size": 32,
                        "learning_rate": 0.001
                    }
                },
                "status": "completed",
                "progress": 1.0,
                "metrics": {
                    "accuracy": 0.95,
                    "f1_score": 0.93
                }
            }
        }


class ModelDocument(BaseDocument):
    """模型文档模型"""
    
    name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    user_id: str
    experiment_id: str
    model_type: str  # sklearn, pytorch, transformers, etc.
    framework: str  # scikit-learn, pytorch, transformers, etc.
    algorithm: str
    version: str = Field(default="1.0.0")
    file_path: str
    file_size: int = Field(..., gt=0)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    performance: Dict[str, Any] = Field(default_factory=dict)
    tags: list = Field(default_factory=list)
    is_deployed: bool = Field(default=False)
    deployment_url: Optional[str] = None
    
    class Config:
        """配置"""
        schema_extra = {
            "example": {
                "name": "CIFAR-10分类模型",
                "description": "基于ResNet18的图像分类模型",
                "experiment_id": "exp_123",
                "model_type": "pytorch",
                "framework": "pytorch",
                "algorithm": "resnet18",
                "version": "1.0.0",
                "file_path": "/models/cifar10_resnet18.pth",
                "file_size": 46827520,
                "metadata": {
                    "num_classes": 10,
                    "input_size": [3, 32, 32],
                    "pretrained": True
                },
                "performance": {
                    "accuracy": 0.95,
                    "f1_score": 0.93,
                    "precision": 0.94,
                    "recall": 0.92
                },
                "tags": ["image_classification", "cifar10", "resnet"],
                "is_deployed": False
            }
        }


class TrainingJobDocument(BaseDocument):
    """训练任务文档模型"""
    
    experiment_id: str
    user_id: str
    status: str = Field(default="pending")  # pending, running, completed, failed, stopped
    progress: float = Field(default=0.0, ge=0.0, le=1.0)
    current_epoch: int = Field(default=0)
    total_epochs: int = Field(default=1)
    current_metrics: Optional[Dict[str, Any]] = None
    logs_path: Optional[str] = None
    pid: Optional[int] = None  # 进程ID
    gpu_ids: list = Field(default_factory=list)  # 使用的GPU ID列表
    estimated_time_remaining: Optional[str] = None
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    error_message: Optional[str] = None
    
    class Config:
        """配置"""
        schema_extra = {
            "example": {
                "experiment_id": "exp_123",
                "status": "running",
                "progress": 0.65,
                "current_epoch": 33,
                "total_epochs": 50,
                "current_metrics": {
                    "train_loss": 0.234,
                    "val_loss": 0.456,
                    "train_accuracy": 0.892,
                    "val_accuracy": 0.834
                },
                "gpu_ids": [0],
                "estimated_time_remaining": "00:15:30"
            }
        }
