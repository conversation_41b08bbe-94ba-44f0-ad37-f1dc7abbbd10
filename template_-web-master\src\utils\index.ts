import dayjs from 'dayjs'
import { isObject, isArray, cloneDeep } from 'lodash-es'

/**
 * 格式化日期
 * @param date 日期
 * @param format 格式
 */
export const formatDate = (date: string | number | Date, format = 'YYYY-MM-DD HH:mm:ss') => {
  return dayjs(date).format(format)
}

/**
 * 深拷贝
 * @param obj 要拷贝的对象
 */
export const deepClone = <T>(obj: T): T => {
  return cloneDeep(obj)
}

/**
 * 防抖函数
 * @param fn 要执行的函数
 * @param delay 延迟时间
 */
export const debounce = <T extends (...args: any[]) => any>(fn: T, delay = 300) => {
  let timer: NodeJS.Timeout | null = null
  return (...args: Parameters<T>) => {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => fn(...args), delay)
  }
}

/**
 * 节流函数
 * @param fn 要执行的函数
 * @param delay 延迟时间
 */
export const throttle = <T extends (...args: any[]) => any>(fn: T, delay = 300) => {
  let timer: NodeJS.Timeout | null = null
  return (...args: Parameters<T>) => {
    if (timer) return
    timer = setTimeout(() => {
      fn(...args)
      timer = null
    }, delay)
  }
}

/**
 * 生成唯一ID
 */
export const generateId = () => {
  return Math.random().toString(36).substr(2, 9)
}

/**
 * 下载文件
 * @param url 文件地址
 * @param filename 文件名
 */
export const downloadFile = (url: string, filename?: string) => {
  const link = document.createElement('a')
  link.href = url
  if (filename) link.download = filename
  link.click()
}

/**
 * 获取文件扩展名
 * @param filename 文件名
 */
export const getFileExtension = (filename: string) => {
  return filename.split('.').pop()?.toLowerCase() || ''
}

/**
 * 格式化文件大小
 * @param size 文件大小（字节）
 */
export const formatFileSize = (size: number) => {
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let index = 0
  while (size >= 1024 && index < units.length - 1) {
    size /= 1024
    index++
  }
  return `${size.toFixed(2)} ${units[index]}`
}

/**
 * 验证邮箱格式
 * @param email 邮箱地址
 */
export const validateEmail = (email: string) => {
  const reg = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return reg.test(email)
}

/**
 * 验证手机号格式
 * @param phone 手机号
 */
export const validatePhone = (phone: string) => {
  const reg = /^1[3-9]\d{9}$/
  return reg.test(phone)
}

/**
 * 树形数据转换为扁平数组
 * @param tree 树形数据
 * @param childrenKey 子节点字段名
 */
export const treeToFlat = <T extends Record<string, any>>(
  tree: T[],
  childrenKey = 'children'
): T[] => {
  const result: T[] = []
  const traverse = (nodes: T[]) => {
    nodes.forEach(node => {
      result.push(node)
      if (node[childrenKey] && isArray(node[childrenKey])) {
        traverse(node[childrenKey])
      }
    })
  }
  traverse(tree)
  return result
}

/**
 * 扁平数组转换为树形数据
 * @param list 扁平数组
 * @param idKey ID字段名
 * @param parentIdKey 父ID字段名
 * @param childrenKey 子节点字段名
 */
export const flatToTree = <T extends Record<string, any>>(
  list: T[],
  idKey = 'id',
  parentIdKey = 'parentId',
  childrenKey = 'children'
): T[] => {
  const map = new Map<any, T>()
  const result: T[] = []

  // 创建映射
  list.forEach(item => {
    map.set(item[idKey], { ...item, [childrenKey]: [] })
  })

  // 构建树形结构
  list.forEach(item => {
    const node = map.get(item[idKey])!
    const parentId = item[parentIdKey]
    
    if (parentId && map.has(parentId)) {
      const parent = map.get(parentId)!
      parent[childrenKey].push(node)
    } else {
      result.push(node)
    }
  })

  return result
}
