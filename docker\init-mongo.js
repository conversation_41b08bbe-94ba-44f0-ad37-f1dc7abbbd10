// TrainAI MongoDB 初始化脚本

// 切换到trainai数据库
db = db.getSiblingDB('trainai');

// 创建用户
db.createUser({
  user: 'trainai_user',
  pwd: 'trainai_password',
  roles: [
    {
      role: 'readWrite',
      db: 'trainai'
    }
  ]
});

// 创建集合和索引
print('Creating collections and indexes...');

// 用户集合
db.users.createIndex({ "username": 1 }, { unique: true });
db.users.createIndex({ "email": 1 }, { unique: true });
db.users.createIndex({ "created_at": 1 });

// 数据集集合
db.datasets.createIndex({ "name": 1 });
db.datasets.createIndex({ "user_id": 1 });
db.datasets.createIndex({ "format": 1 });
db.datasets.createIndex({ "status": 1 });
db.datasets.createIndex({ "created_at": 1 });

// 实验集合
db.experiments.createIndex({ "name": 1 });
db.experiments.createIndex({ "user_id": 1 });
db.experiments.createIndex({ "dataset_id": 1 });
db.experiments.createIndex({ "status": 1 });
db.experiments.createIndex({ "model_type": 1 });
db.experiments.createIndex({ "algorithm": 1 });
db.experiments.createIndex({ "created_at": 1 });

// 模型集合
db.models.createIndex({ "name": 1 });
db.models.createIndex({ "user_id": 1 });
db.models.createIndex({ "experiment_id": 1 });
db.models.createIndex({ "model_type": 1 });
db.models.createIndex({ "framework": 1 });
db.models.createIndex({ "algorithm": 1 });
db.models.createIndex({ "is_deployed": 1 });
db.models.createIndex({ "created_at": 1 });

// 训练任务集合
db.training_jobs.createIndex({ "experiment_id": 1 });
db.training_jobs.createIndex({ "user_id": 1 });
db.training_jobs.createIndex({ "status": 1 });
db.training_jobs.createIndex({ "created_at": 1 });

// 插入默认管理员用户
db.users.insertOne({
  username: 'admin',
  email: '<EMAIL>',
  full_name: 'TrainAI Administrator',
  hashed_password: '$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW', // admin123
  is_active: true,
  is_superuser: true,
  permissions: ['read', 'write', 'admin'],
  created_at: new Date().getTime() / 1000,
  updated_at: new Date().getTime() / 1000
});

print('MongoDB initialization completed successfully!');
print('Default admin user created:');
print('  Username: admin');
print('  Password: admin123');
print('  Email: <EMAIL>');
