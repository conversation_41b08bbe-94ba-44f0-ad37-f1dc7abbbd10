"""
TrainAI Backend 配置管理

使用Pydantic Settings进行配置管理，支持从环境变量和.env文件读取配置。
"""

from pydantic import BaseSettings, validator
from typing import List, Optional, Union
import os
from pathlib import Path


class Settings(BaseSettings):
    """应用配置类"""
    
    # ================================
    # 应用基础配置
    # ================================
    APP_NAME: str = "TrainAI"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    ENVIRONMENT: str = "development"
    SECRET_KEY: str = "your-super-secret-key-change-this-in-production"
    
    # ================================
    # 服务端口配置
    # ================================
    BACKEND_PORT: int = 8000
    FRONTEND_PORT: int = 3000
    
    # ================================
    # 数据库配置
    # ================================
    # MongoDB
    MONGODB_URL: str = "mongodb://localhost:27017/trainai"
    MONGODB_DB_NAME: str = "trainai"
    
    # Redis
    REDIS_URL: str = "redis://localhost:6379/0"
    REDIS_PASSWORD: Optional[str] = None
    
    # ================================
    # 对象存储配置 (MinIO)
    # ================================
    MINIO_ENDPOINT: str = "localhost:9000"
    MINIO_ACCESS_KEY: str = "minioadmin"
    MINIO_SECRET_KEY: str = "minioadmin"
    MINIO_BUCKET: str = "trainai-storage"
    MINIO_SECURE: bool = False
    
    # ================================
    # 认证配置
    # ================================
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    ALGORITHM: str = "HS256"
    
    # ================================
    # 文件上传配置
    # ================================
    MAX_FILE_SIZE: int = 1073741824  # 1GB
    ALLOWED_EXTENSIONS: str = "csv,json,parquet,jpg,jpeg,png,bmp,txt,pkl,pth,onnx"
    
    @validator("ALLOWED_EXTENSIONS")
    def parse_allowed_extensions(cls, v):
        """解析允许的文件扩展名"""
        return [ext.strip().lower() for ext in v.split(",")]
    
    # ================================
    # GPU和计算资源配置
    # ================================
    CUDA_VISIBLE_DEVICES: Optional[str] = None
    MAX_GPU_MEMORY: int = 8192  # MB
    MAX_CONCURRENT_TRAINING: int = 3
    DEFAULT_BATCH_SIZE: int = 32
    
    # ================================
    # 日志配置
    # ================================
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_FILE: str = "logs/trainai.log"
    
    # ================================
    # CORS配置
    # ================================
    ENABLE_CORS: bool = True
    CORS_ORIGINS: str = "http://localhost:3000,http://127.0.0.1:3000"
    
    @validator("CORS_ORIGINS")
    def parse_cors_origins(cls, v):
        """解析CORS允许的源"""
        return [origin.strip() for origin in v.split(",")]
    
    # ================================
    # API文档配置
    # ================================
    ENABLE_DOCS: bool = True
    
    # ================================
    # 邮件配置 (可选)
    # ================================
    SMTP_HOST: Optional[str] = None
    SMTP_PORT: int = 587
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    SMTP_FROM: str = "TrainAI <<EMAIL>>"
    
    # ================================
    # 监控配置
    # ================================
    PROMETHEUS_PORT: int = 9090
    GRAFANA_PORT: int = 3001
    
    # ================================
    # 路径配置
    # ================================
    @property
    def BASE_DIR(self) -> Path:
        """项目根目录"""
        return Path(__file__).parent.parent.parent.parent
    
    @property
    def UPLOAD_DIR(self) -> Path:
        """上传文件目录"""
        upload_dir = self.BASE_DIR / "uploads"
        upload_dir.mkdir(exist_ok=True)
        return upload_dir
    
    @property
    def MODELS_DIR(self) -> Path:
        """模型存储目录"""
        models_dir = self.BASE_DIR / "models"
        models_dir.mkdir(exist_ok=True)
        return models_dir
    
    @property
    def LOGS_DIR(self) -> Path:
        """日志目录"""
        logs_dir = self.BASE_DIR / "logs"
        logs_dir.mkdir(exist_ok=True)
        return logs_dir
    
    @property
    def TEMP_DIR(self) -> Path:
        """临时文件目录"""
        temp_dir = self.BASE_DIR / "temp"
        temp_dir.mkdir(exist_ok=True)
        return temp_dir
    
    # ================================
    # 验证器
    # ================================
    @validator("SECRET_KEY")
    def validate_secret_key(cls, v):
        """验证密钥强度"""
        if len(v) < 32:
            raise ValueError("SECRET_KEY must be at least 32 characters long")
        return v
    
    @validator("MAX_FILE_SIZE")
    def validate_max_file_size(cls, v):
        """验证最大文件大小"""
        if v <= 0:
            raise ValueError("MAX_FILE_SIZE must be positive")
        return v
    
    @validator("MAX_CONCURRENT_TRAINING")
    def validate_max_concurrent_training(cls, v):
        """验证最大并发训练数"""
        if v <= 0:
            raise ValueError("MAX_CONCURRENT_TRAINING must be positive")
        return v
    
    # ================================
    # 环境特定配置
    # ================================
    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.ENVIRONMENT.lower() == "development"
    
    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.ENVIRONMENT.lower() == "production"
    
    @property
    def is_testing(self) -> bool:
        """是否为测试环境"""
        return self.ENVIRONMENT.lower() == "testing"
    
    class Config:
        """Pydantic配置"""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# 创建全局配置实例
settings = Settings()


# ================================
# 配置验证和初始化
# ================================

def validate_settings():
    """验证配置设置"""
    errors = []
    
    # 验证必要的目录权限
    try:
        settings.UPLOAD_DIR.mkdir(exist_ok=True)
        settings.MODELS_DIR.mkdir(exist_ok=True)
        settings.LOGS_DIR.mkdir(exist_ok=True)
        settings.TEMP_DIR.mkdir(exist_ok=True)
    except PermissionError as e:
        errors.append(f"Directory permission error: {e}")
    
    # 验证GPU配置
    if settings.CUDA_VISIBLE_DEVICES:
        try:
            import torch
            if not torch.cuda.is_available():
                errors.append("CUDA is not available but CUDA_VISIBLE_DEVICES is set")
        except ImportError:
            errors.append("PyTorch is not installed but CUDA_VISIBLE_DEVICES is set")
    
    # 验证数据库连接字符串
    if not settings.MONGODB_URL.startswith(("mongodb://", "mongodb+srv://")):
        errors.append("Invalid MongoDB URL format")
    
    if not settings.REDIS_URL.startswith("redis://"):
        errors.append("Invalid Redis URL format")
    
    if errors:
        raise ValueError(f"Configuration validation failed: {'; '.join(errors)}")


def print_settings():
    """打印当前配置（隐藏敏感信息）"""
    print("🔧 TrainAI Configuration:")
    print(f"  App Name: {settings.APP_NAME}")
    print(f"  Version: {settings.APP_VERSION}")
    print(f"  Environment: {settings.ENVIRONMENT}")
    print(f"  Debug: {settings.DEBUG}")
    print(f"  Backend Port: {settings.BACKEND_PORT}")
    print(f"  MongoDB: {settings.MONGODB_URL.split('@')[-1] if '@' in settings.MONGODB_URL else settings.MONGODB_URL}")
    print(f"  Redis: {settings.REDIS_URL.split('@')[-1] if '@' in settings.REDIS_URL else settings.REDIS_URL}")
    print(f"  MinIO: {settings.MINIO_ENDPOINT}")
    print(f"  Max File Size: {settings.MAX_FILE_SIZE / 1024 / 1024:.0f}MB")
    print(f"  Max Concurrent Training: {settings.MAX_CONCURRENT_TRAINING}")
    print(f"  CORS Enabled: {settings.ENABLE_CORS}")
    print(f"  Docs Enabled: {settings.ENABLE_DOCS}")


# 在导入时验证配置
if __name__ != "__main__":
    try:
        validate_settings()
    except ValueError as e:
        print(f"⚠️  Configuration warning: {e}")


if __name__ == "__main__":
    # 当直接运行此文件时，打印配置信息
    print_settings()
    try:
        validate_settings()
        print("✅ Configuration validation passed")
    except ValueError as e:
        print(f"❌ Configuration validation failed: {e}")
