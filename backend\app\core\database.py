"""
TrainAI Backend 数据库连接管理

提供MongoDB和Redis的异步连接管理功能。
"""

import logging
from typing import Optional
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
import aioredis
from aioredis import Redis

from app.core.config import settings

logger = logging.getLogger(__name__)

# ================================
# MongoDB 连接管理
# ================================

class MongoManager:
    """MongoDB连接管理器"""
    
    def __init__(self):
        self.client: Optional[AsyncIOMotorClient] = None
        self.database: Optional[AsyncIOMotorDatabase] = None
    
    async def connect(self):
        """连接到MongoDB"""
        try:
            logger.info("🔌 正在连接MongoDB...")
            
            # 创建MongoDB客户端
            self.client = AsyncIOMotorClient(
                settings.MONGODB_URL,
                maxPoolSize=10,
                minPoolSize=1,
                maxIdleTimeMS=30000,
                waitQueueTimeoutMS=5000,
                serverSelectionTimeoutMS=5000
            )
            
            # 获取数据库
            self.database = self.client[settings.MONGODB_DB_NAME]
            
            # 测试连接
            await self.client.admin.command('ping')
            
            logger.info(f"✅ MongoDB连接成功: {settings.MONGODB_DB_NAME}")
            
            # 创建索引
            await self._create_indexes()
            
        except Exception as e:
            logger.error(f"❌ MongoDB连接失败: {e}")
            raise
    
    async def disconnect(self):
        """断开MongoDB连接"""
        if self.client:
            logger.info("🔌 正在断开MongoDB连接...")
            self.client.close()
            self.client = None
            self.database = None
            logger.info("✅ MongoDB连接已断开")
    
    async def _create_indexes(self):
        """创建数据库索引"""
        try:
            # 用户集合索引
            await self.database.users.create_index("username", unique=True)
            await self.database.users.create_index("email", unique=True)
            
            # 数据集集合索引
            await self.database.datasets.create_index("name")
            await self.database.datasets.create_index("user_id")
            await self.database.datasets.create_index("created_at")
            
            # 实验集合索引
            await self.database.experiments.create_index("name")
            await self.database.experiments.create_index("user_id")
            await self.database.experiments.create_index("dataset_id")
            await self.database.experiments.create_index("status")
            await self.database.experiments.create_index("created_at")
            
            # 模型集合索引
            await self.database.models.create_index("name")
            await self.database.models.create_index("user_id")
            await self.database.models.create_index("experiment_id")
            await self.database.models.create_index("model_type")
            await self.database.models.create_index("created_at")
            
            logger.info("✅ 数据库索引创建完成")
            
        except Exception as e:
            logger.warning(f"⚠️  索引创建警告: {e}")
    
    def get_database(self) -> AsyncIOMotorDatabase:
        """获取数据库实例"""
        if not self.database:
            raise RuntimeError("Database not connected. Call connect() first.")
        return self.database


# ================================
# Redis 连接管理
# ================================

class RedisManager:
    """Redis连接管理器"""
    
    def __init__(self):
        self.redis: Optional[Redis] = None
    
    async def connect(self):
        """连接到Redis"""
        try:
            logger.info("🔌 正在连接Redis...")
            
            # 解析Redis URL
            redis_url = settings.REDIS_URL
            if settings.REDIS_PASSWORD:
                # 如果有密码，添加到URL中
                if "://" in redis_url:
                    protocol, rest = redis_url.split("://", 1)
                    redis_url = f"{protocol}://:{settings.REDIS_PASSWORD}@{rest}"
            
            # 创建Redis连接
            self.redis = await aioredis.from_url(
                redis_url,
                encoding="utf-8",
                decode_responses=True,
                max_connections=20,
                retry_on_timeout=True
            )
            
            # 测试连接
            await self.redis.ping()
            
            logger.info("✅ Redis连接成功")
            
        except Exception as e:
            logger.error(f"❌ Redis连接失败: {e}")
            raise
    
    async def disconnect(self):
        """断开Redis连接"""
        if self.redis:
            logger.info("🔌 正在断开Redis连接...")
            await self.redis.close()
            self.redis = None
            logger.info("✅ Redis连接已断开")
    
    def get_redis(self) -> Redis:
        """获取Redis实例"""
        if not self.redis:
            raise RuntimeError("Redis not connected. Call connect() first.")
        return self.redis


# ================================
# 全局连接管理器实例
# ================================

mongo_manager = MongoManager()
redis_manager = RedisManager()


# ================================
# 便捷函数
# ================================

async def connect_to_mongo():
    """连接到MongoDB"""
    await mongo_manager.connect()


async def close_mongo_connection():
    """关闭MongoDB连接"""
    await mongo_manager.disconnect()


async def connect_to_redis():
    """连接到Redis"""
    await redis_manager.connect()


async def close_redis_connection():
    """关闭Redis连接"""
    await redis_manager.disconnect()


def get_database() -> AsyncIOMotorDatabase:
    """获取MongoDB数据库实例"""
    return mongo_manager.get_database()


def get_redis() -> Redis:
    """获取Redis实例"""
    return redis_manager.get_redis()


# ================================
# 数据库操作辅助函数
# ================================

async def check_database_health() -> dict:
    """检查数据库健康状态"""
    health_status = {
        "mongodb": {"status": "unknown", "error": None},
        "redis": {"status": "unknown", "error": None}
    }
    
    # 检查MongoDB
    try:
        if mongo_manager.client:
            await mongo_manager.client.admin.command('ping')
            health_status["mongodb"]["status"] = "healthy"
        else:
            health_status["mongodb"]["status"] = "disconnected"
    except Exception as e:
        health_status["mongodb"]["status"] = "error"
        health_status["mongodb"]["error"] = str(e)
    
    # 检查Redis
    try:
        if redis_manager.redis:
            await redis_manager.redis.ping()
            health_status["redis"]["status"] = "healthy"
        else:
            health_status["redis"]["status"] = "disconnected"
    except Exception as e:
        health_status["redis"]["status"] = "error"
        health_status["redis"]["error"] = str(e)
    
    return health_status


async def get_database_stats() -> dict:
    """获取数据库统计信息"""
    stats = {}
    
    try:
        if mongo_manager.database:
            # MongoDB统计
            db_stats = await mongo_manager.database.command("dbStats")
            collection_names = await mongo_manager.database.list_collection_names()
            
            stats["mongodb"] = {
                "database_name": settings.MONGODB_DB_NAME,
                "collections_count": len(collection_names),
                "collections": collection_names,
                "data_size": db_stats.get("dataSize", 0),
                "storage_size": db_stats.get("storageSize", 0),
                "indexes": db_stats.get("indexes", 0)
            }
    except Exception as e:
        stats["mongodb"] = {"error": str(e)}
    
    try:
        if redis_manager.redis:
            # Redis统计
            redis_info = await redis_manager.redis.info()
            stats["redis"] = {
                "version": redis_info.get("redis_version"),
                "used_memory": redis_info.get("used_memory"),
                "connected_clients": redis_info.get("connected_clients"),
                "total_commands_processed": redis_info.get("total_commands_processed")
            }
    except Exception as e:
        stats["redis"] = {"error": str(e)}
    
    return stats


# ================================
# 数据库初始化
# ================================

async def init_database():
    """初始化数据库"""
    logger.info("🔧 正在初始化数据库...")
    
    try:
        # 连接数据库
        await connect_to_mongo()
        await connect_to_redis()
        
        # 创建默认数据
        await _create_default_data()
        
        logger.info("✅ 数据库初始化完成")
        
    except Exception as e:
        logger.error(f"❌ 数据库初始化失败: {e}")
        raise


async def _create_default_data():
    """创建默认数据"""
    try:
        db = get_database()
        
        # 检查是否已有管理员用户
        admin_user = await db.users.find_one({"username": "admin"})
        if not admin_user:
            # 创建默认管理员用户
            from app.core.security import get_password_hash
            
            default_admin = {
                "username": "admin",
                "email": "<EMAIL>",
                "hashed_password": get_password_hash("admin123"),
                "is_active": True,
                "is_superuser": True,
                "created_at": "2024-01-01T00:00:00Z"
            }
            
            await db.users.insert_one(default_admin)
            logger.info("✅ 默认管理员用户创建成功")
        
    except Exception as e:
        logger.warning(f"⚠️  默认数据创建警告: {e}")


if __name__ == "__main__":
    # 测试数据库连接
    import asyncio
    
    async def test_connections():
        try:
            await connect_to_mongo()
            await connect_to_redis()
            
            health = await check_database_health()
            print("数据库健康状态:", health)
            
            stats = await get_database_stats()
            print("数据库统计:", stats)
            
        finally:
            await close_mongo_connection()
            await close_redis_connection()
    
    asyncio.run(test_connections())
