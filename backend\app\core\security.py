"""
TrainAI Backend 安全认证模块

提供JWT认证、密码哈希、权限验证等安全功能。
"""

from datetime import datetime, timedelta
from typing import Optional, Union, Any
import jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status, Depends
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

# ================================
# 密码加密配置
# ================================

# 密码上下文配置
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT Bearer认证
security = HTTPBearer()


# ================================
# 密码处理函数
# ================================

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    try:
        return pwd_context.verify(plain_password, hashed_password)
    except Exception as e:
        logger.error(f"密码验证错误: {e}")
        return False


def get_password_hash(password: str) -> str:
    """生成密码哈希"""
    try:
        return pwd_context.hash(password)
    except Exception as e:
        logger.error(f"密码哈希生成错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="密码处理失败"
        )


# ================================
# JWT Token处理
# ================================

def create_access_token(
    data: dict, 
    expires_delta: Optional[timedelta] = None
) -> str:
    """创建访问令牌"""
    to_encode = data.copy()
    
    # 设置过期时间
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
    
    to_encode.update({
        "exp": expire,
        "iat": datetime.utcnow(),
        "type": "access"
    })
    
    try:
        encoded_jwt = jwt.encode(
            to_encode, 
            settings.SECRET_KEY, 
            algorithm=settings.ALGORITHM
        )
        return encoded_jwt
    except Exception as e:
        logger.error(f"JWT令牌创建错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="令牌创建失败"
        )


def create_refresh_token(data: dict) -> str:
    """创建刷新令牌"""
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
    
    to_encode.update({
        "exp": expire,
        "iat": datetime.utcnow(),
        "type": "refresh"
    })
    
    try:
        encoded_jwt = jwt.encode(
            to_encode,
            settings.SECRET_KEY,
            algorithm=settings.ALGORITHM
        )
        return encoded_jwt
    except Exception as e:
        logger.error(f"刷新令牌创建错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="刷新令牌创建失败"
        )


def verify_token(token: str, token_type: str = "access") -> dict:
    """验证JWT令牌"""
    try:
        payload = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )
        
        # 检查令牌类型
        if payload.get("type") != token_type:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌类型错误"
            )
        
        # 检查是否过期
        exp = payload.get("exp")
        if exp and datetime.utcnow().timestamp() > exp:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌已过期"
            )
        
        return payload
        
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="令牌已过期"
        )
    except jwt.JWTError as e:
        logger.error(f"JWT验证错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="令牌验证失败"
        )


# ================================
# 认证依赖函数
# ================================

async def get_current_user_id(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> str:
    """获取当前用户ID"""
    try:
        token = credentials.credentials
        payload = verify_token(token, "access")
        user_id = payload.get("sub")
        
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌中缺少用户信息"
            )
        
        return user_id
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户认证错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="认证失败"
        )


async def get_current_user(
    user_id: str = Depends(get_current_user_id)
) -> dict:
    """获取当前用户信息"""
    from app.core.database import get_database
    from bson import ObjectId
    
    try:
        db = get_database()
        user = await db.users.find_one({"_id": ObjectId(user_id)})
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        if not user.get("is_active", True):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户账户已被禁用"
            )
        
        # 转换ObjectId为字符串
        user["id"] = str(user["_id"])
        del user["_id"]
        
        # 移除敏感信息
        user.pop("hashed_password", None)
        
        return user
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户信息错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户信息失败"
        )


async def get_current_active_user(
    current_user: dict = Depends(get_current_user)
) -> dict:
    """获取当前活跃用户"""
    if not current_user.get("is_active", True):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户已被禁用"
        )
    return current_user


async def get_current_superuser(
    current_user: dict = Depends(get_current_user)
) -> dict:
    """获取当前超级用户"""
    if not current_user.get("is_superuser", False):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要超级用户权限"
        )
    return current_user


# ================================
# 权限验证函数
# ================================

def check_user_permission(user: dict, required_permission: str) -> bool:
    """检查用户权限"""
    # 超级用户拥有所有权限
    if user.get("is_superuser", False):
        return True
    
    # 检查用户角色权限
    user_permissions = user.get("permissions", [])
    return required_permission in user_permissions


def require_permission(permission: str):
    """权限装饰器"""
    def permission_checker(current_user: dict = Depends(get_current_user)):
        if not check_user_permission(current_user, permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"权限不足，需要 {permission} 权限"
            )
        return current_user
    return permission_checker


# ================================
# 资源所有权验证
# ================================

async def check_resource_ownership(
    user_id: str, 
    resource_id: str, 
    collection_name: str
) -> bool:
    """检查资源所有权"""
    from app.core.database import get_database
    from bson import ObjectId
    
    try:
        db = get_database()
        resource = await db[collection_name].find_one({"_id": ObjectId(resource_id)})
        
        if not resource:
            return False
        
        return str(resource.get("user_id")) == user_id
        
    except Exception as e:
        logger.error(f"检查资源所有权错误: {e}")
        return False


def require_resource_ownership(collection_name: str):
    """资源所有权装饰器"""
    async def ownership_checker(
        resource_id: str,
        current_user: dict = Depends(get_current_user)
    ):
        # 超级用户可以访问所有资源
        if current_user.get("is_superuser", False):
            return current_user
        
        # 检查资源所有权
        if not await check_resource_ownership(
            current_user["id"], 
            resource_id, 
            collection_name
        ):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此资源"
            )
        
        return current_user
    
    return ownership_checker


# ================================
# 令牌工具函数
# ================================

def decode_token_without_verification(token: str) -> dict:
    """解码令牌但不验证（用于调试）"""
    try:
        return jwt.decode(token, options={"verify_signature": False})
    except Exception as e:
        logger.error(f"令牌解码错误: {e}")
        return {}


def get_token_expiry(token: str) -> Optional[datetime]:
    """获取令牌过期时间"""
    try:
        payload = decode_token_without_verification(token)
        exp = payload.get("exp")
        if exp:
            return datetime.fromtimestamp(exp)
        return None
    except Exception:
        return None


def is_token_expired(token: str) -> bool:
    """检查令牌是否过期"""
    expiry = get_token_expiry(token)
    if expiry:
        return datetime.utcnow() > expiry
    return True


# ================================
# 安全工具函数
# ================================

def generate_secure_token(length: int = 32) -> str:
    """生成安全随机令牌"""
    import secrets
    import string
    
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(length))


def validate_password_strength(password: str) -> tuple[bool, str]:
    """验证密码强度"""
    if len(password) < 8:
        return False, "密码长度至少8位"
    
    if not any(c.isupper() for c in password):
        return False, "密码必须包含大写字母"
    
    if not any(c.islower() for c in password):
        return False, "密码必须包含小写字母"
    
    if not any(c.isdigit() for c in password):
        return False, "密码必须包含数字"
    
    special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
    if not any(c in special_chars for c in password):
        return False, "密码必须包含特殊字符"
    
    return True, "密码强度符合要求"


if __name__ == "__main__":
    # 测试安全功能
    print("🔐 测试安全功能...")
    
    # 测试密码哈希
    password = "test123"
    hashed = get_password_hash(password)
    print(f"原密码: {password}")
    print(f"哈希值: {hashed}")
    print(f"验证结果: {verify_password(password, hashed)}")
    
    # 测试JWT令牌
    test_data = {"sub": "user123", "username": "testuser"}
    access_token = create_access_token(test_data)
    print(f"访问令牌: {access_token}")
    
    try:
        payload = verify_token(access_token)
        print(f"令牌验证成功: {payload}")
    except Exception as e:
        print(f"令牌验证失败: {e}")
    
    # 测试密码强度
    test_passwords = ["123", "password", "Password1", "Password1!"]
    for pwd in test_passwords:
        valid, msg = validate_password_strength(pwd)
        print(f"密码 '{pwd}': {msg}")
