# TrainAI 开发指南

## 1. 环境准备

### 1.1 系统要求
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **Python**: 3.8+
- **Node.js**: 16+
- **Docker**: 20.10+
- **GPU**: NVIDIA GPU (可选，用于深度学习训练)

### 1.2 开发工具推荐
- **IDE**: VS Code, PyCharm
- **API测试**: Postman, Insomnia
- **数据库管理**: MongoDB Compass
- **版本控制**: Git

### 1.3 环境变量配置
创建 `.env` 文件：
```bash
# 应用配置
APP_NAME=TrainAI
DEBUG=true
SECRET_KEY=your-super-secret-key-here

# 数据库配置
MONGODB_URL=mongodb://localhost:27017/trainai
REDIS_URL=redis://localhost:6379/0

# 存储配置
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET=trainai-storage

# GPU配置
CUDA_VISIBLE_DEVICES=0
MAX_GPU_MEMORY=8192

# 前端配置
REACT_APP_API_URL=http://localhost:8000
REACT_APP_WS_URL=ws://localhost:8000
```

## 2. 快速开始

### 2.1 克隆项目
```bash
git clone https://github.com/your-org/TrainAI.git
cd TrainAI
```

### 2.2 使用Docker快速启动
```bash
# 启动所有服务
docker-compose -f docker/docker-compose.dev.yml up -d

# 查看服务状态
docker-compose -f docker/docker-compose.dev.yml ps

# 查看日志
docker-compose -f docker/docker-compose.dev.yml logs -f api
```

### 2.3 本地开发环境
```bash
# 后端开发
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 前端开发
cd frontend
npm install
npm start
```

## 3. 项目结构详解

### 3.1 后端结构
```
backend/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI应用入口
│   ├── api/                 # API路由
│   │   ├── __init__.py
│   │   ├── deps.py          # 依赖注入
│   │   └── v1/              # API v1版本
│   │       ├── __init__.py
│   │       ├── auth.py      # 认证相关
│   │       ├── datasets.py  # 数据集管理
│   │       ├── experiments.py # 实验管理
│   │       ├── models.py    # 模型管理
│   │       └── training.py  # 训练管理
│   ├── core/                # 核心配置
│   │   ├── __init__.py
│   │   ├── config.py        # 配置管理
│   │   ├── security.py      # 安全相关
│   │   └── database.py      # 数据库连接
│   ├── models/              # 数据模型
│   │   ├── __init__.py
│   │   ├── base.py          # 基础模型
│   │   ├── user.py          # 用户模型
│   │   ├── dataset.py       # 数据集模型
│   │   ├── experiment.py    # 实验模型
│   │   └── model.py         # AI模型元数据
│   ├── services/            # 业务逻辑
│   │   ├── __init__.py
│   │   ├── auth_service.py  # 认证服务
│   │   ├── data_service.py  # 数据服务
│   │   ├── training_service.py # 训练服务
│   │   ├── model_service.py # 模型服务
│   │   └── experiment_service.py # 实验服务
│   ├── engines/             # AI训练引擎
│   │   ├── __init__.py
│   │   ├── base_engine.py   # 基础引擎接口
│   │   ├── sklearn_engine.py # Sklearn引擎
│   │   ├── pytorch_engine.py # PyTorch引擎
│   │   ├── transformers_engine.py # Transformers引擎
│   │   └── generative_engine.py # 生成式AI引擎
│   └── utils/               # 工具函数
│       ├── __init__.py
│       ├── file_utils.py    # 文件操作
│       ├── data_utils.py    # 数据处理
│       ├── model_utils.py   # 模型工具
│       └── monitoring.py    # 监控工具
├── requirements.txt         # Python依赖
├── Dockerfile              # Docker配置
└── pytest.ini             # 测试配置
```

### 3.2 前端结构
```
frontend/
├── public/                  # 静态资源
├── src/
│   ├── components/          # 通用组件
│   │   ├── Layout/          # 布局组件
│   │   ├── DataUpload/      # 数据上传组件
│   │   ├── ModelCard/       # 模型卡片组件
│   │   ├── ExperimentTable/ # 实验表格组件
│   │   ├── TrainingMonitor/ # 训练监控组件
│   │   └── Charts/          # 图表组件
│   ├── pages/               # 页面组件
│   │   ├── Dashboard/       # 仪表板
│   │   ├── Datasets/        # 数据集管理
│   │   ├── Experiments/     # 实验管理
│   │   ├── Models/          # 模型管理
│   │   ├── Training/        # 训练监控
│   │   └── Settings/        # 设置页面
│   ├── services/            # API服务
│   │   ├── api.ts           # API基础配置
│   │   ├── auth.ts          # 认证服务
│   │   ├── datasets.ts      # 数据集服务
│   │   ├── experiments.ts   # 实验服务
│   │   ├── models.ts        # 模型服务
│   │   └── websocket.ts     # WebSocket服务
│   ├── store/               # 状态管理
│   │   ├── index.ts         # Store配置
│   │   ├── slices/          # Redux切片
│   │   │   ├── authSlice.ts
│   │   │   ├── datasetSlice.ts
│   │   │   ├── experimentSlice.ts
│   │   │   └── trainingSlice.ts
│   │   └── middleware/      # 中间件
│   ├── utils/               # 工具函数
│   │   ├── constants.ts     # 常量定义
│   │   ├── helpers.ts       # 辅助函数
│   │   ├── formatters.ts    # 格式化函数
│   │   └── validators.ts    # 验证函数
│   ├── types/               # TypeScript类型
│   │   ├── api.ts           # API类型
│   │   ├── dataset.ts       # 数据集类型
│   │   ├── experiment.ts    # 实验类型
│   │   └── model.ts         # 模型类型
│   ├── styles/              # 样式文件
│   │   ├── globals.css      # 全局样式
│   │   └── components/      # 组件样式
│   ├── App.tsx              # 应用根组件
│   └── index.tsx            # 应用入口
├── package.json             # Node.js依赖
├── tsconfig.json           # TypeScript配置
└── Dockerfile              # Docker配置
```

## 4. 开发规范

### 4.1 代码规范
- **Python**: 遵循PEP 8规范，使用black格式化
- **TypeScript**: 使用ESLint + Prettier
- **Git提交**: 使用Conventional Commits规范

### 4.2 命名规范
```python
# Python命名规范
class DatasetService:           # 类名：PascalCase
    def create_dataset(self):   # 方法名：snake_case
        dataset_id = "123"      # 变量名：snake_case
        
# 常量
MAX_FILE_SIZE = 1024 * 1024 * 1024  # 全大写+下划线
```

```typescript
// TypeScript命名规范
interface ExperimentConfig {    // 接口：PascalCase
  modelType: string;           // 属性：camelCase
  trainingParams: object;
}

const createExperiment = () => {}; // 函数：camelCase
const API_BASE_URL = 'http://...'; // 常量：全大写+下划线
```

### 4.3 API设计规范
```python
# RESTful API设计
GET    /api/v1/datasets          # 获取列表
POST   /api/v1/datasets          # 创建资源
GET    /api/v1/datasets/{id}     # 获取单个资源
PUT    /api/v1/datasets/{id}     # 更新资源
DELETE /api/v1/datasets/{id}     # 删除资源

# 嵌套资源
GET    /api/v1/datasets/{id}/experiments  # 获取数据集的实验列表
POST   /api/v1/experiments/{id}/start     # 执行动作
```

## 5. 测试指南

### 5.1 后端测试
```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_datasets.py

# 运行测试并生成覆盖率报告
pytest --cov=app tests/

# 运行测试并生成HTML覆盖率报告
pytest --cov=app --cov-report=html tests/
```

### 5.2 前端测试
```bash
# 运行单元测试
npm test

# 运行测试并生成覆盖率报告
npm run test:coverage

# 运行E2E测试
npm run test:e2e
```

### 5.3 测试示例
```python
# backend/tests/test_datasets.py
import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_create_dataset():
    response = client.post(
        "/api/v1/datasets",
        json={
            "name": "测试数据集",
            "description": "这是一个测试数据集"
        }
    )
    assert response.status_code == 201
    assert response.json()["name"] == "测试数据集"
```

## 6. 部署指南

### 6.1 开发环境部署
```bash
# 使用Docker Compose
docker-compose -f docker/docker-compose.dev.yml up -d

# 检查服务状态
docker-compose -f docker/docker-compose.dev.yml ps
```

### 6.2 生产环境部署
```bash
# 构建镜像
docker build -t trainai-backend:latest backend/
docker build -t trainai-frontend:latest frontend/

# 部署到Kubernetes
kubectl apply -f k8s/
```

### 6.3 环境变量配置
```bash
# 生产环境变量
export APP_ENV=production
export DEBUG=false
export MONGODB_URL=mongodb://prod-mongo:27017/trainai
export REDIS_URL=redis://prod-redis:6379/0
```

## 7. 调试技巧

### 7.1 后端调试
```python
# 使用logging
import logging
logger = logging.getLogger(__name__)

def train_model():
    logger.info("开始训练模型")
    logger.debug(f"模型参数: {params}")
    logger.error("训练失败", exc_info=True)
```

### 7.2 前端调试
```typescript
// 使用console调试
console.log('API响应:', response);
console.error('请求失败:', error);

// 使用Redux DevTools
// 安装Redux DevTools扩展
```

### 7.3 性能监控
```python
# 使用装饰器监控函数执行时间
import time
from functools import wraps

def timing_decorator(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start = time.time()
        result = func(*args, **kwargs)
        end = time.time()
        print(f"{func.__name__} 执行时间: {end - start:.2f}秒")
        return result
    return wrapper

@timing_decorator
def train_model():
    # 训练逻辑
    pass
```

## 8. 常见问题

### 8.1 环境问题
**Q: CUDA版本不匹配怎么办？**
A: 检查CUDA版本并安装对应的PyTorch版本：
```bash
nvidia-smi  # 查看CUDA版本
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
```

**Q: 内存不足怎么办？**
A: 调整批次大小和模型参数：
```python
# 减小batch_size
config['batch_size'] = 16  # 从32减少到16

# 使用梯度累积
config['gradient_accumulation_steps'] = 2
```

### 8.2 开发问题
**Q: API请求跨域问题？**
A: 在FastAPI中配置CORS：
```python
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

**Q: WebSocket连接失败？**
A: 检查防火墙和代理设置，确保WebSocket端口可访问。
