# TrainAI 开发环境 Docker Compose 配置

version: '3.8'

services:
  # ================================
  # 后端API服务
  # ================================
  api:
    build:
      context: ../backend
      dockerfile: Dockerfile
    container_name: trainai-api
    ports:
      - "8000:8000"
    environment:
      - DEBUG=true
      - ENVIRONMENT=development
      - MONGODB_URL=mongodb://mongodb:27017/trainai
      - REDIS_URL=redis://redis:6379/0
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - SECRET_KEY=dev-secret-key-change-in-production
      - ENABLE_CORS=true
      - CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
    volumes:
      - ../backend:/app
      - trainai-uploads:/app/uploads
      - trainai-models:/app/models
      - trainai-logs:/app/logs
    depends_on:
      - mongodb
      - redis
      - minio
    restart: unless-stopped
    networks:
      - trainai-network

  # ================================
  # MongoDB 数据库
  # ================================
  mongodb:
    image: mongo:7.0
    container_name: trainai-mongodb
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=admin123
      - MONGO_INITDB_DATABASE=trainai
    volumes:
      - trainai-mongodb-data:/data/db
      - ./init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    restart: unless-stopped
    networks:
      - trainai-network

  # ================================
  # Redis 缓存
  # ================================
  redis:
    image: redis:7.2-alpine
    container_name: trainai-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass redis123
    volumes:
      - trainai-redis-data:/data
    restart: unless-stopped
    networks:
      - trainai-network

  # ================================
  # MinIO 对象存储
  # ================================
  minio:
    image: minio/minio:latest
    container_name: trainai-minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    command: server /data --console-address ":9001"
    volumes:
      - trainai-minio-data:/data
    restart: unless-stopped
    networks:
      - trainai-network

  # ================================
  # Celery Worker (任务队列)
  # ================================
  celery-worker:
    build:
      context: ../backend
      dockerfile: Dockerfile
    container_name: trainai-celery-worker
    command: celery -A app.core.celery worker --loglevel=info
    environment:
      - DEBUG=true
      - ENVIRONMENT=development
      - MONGODB_URL=mongodb://mongodb:27017/trainai
      - REDIS_URL=redis://redis:6379/0
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
    volumes:
      - ../backend:/app
      - trainai-uploads:/app/uploads
      - trainai-models:/app/models
      - trainai-logs:/app/logs
    depends_on:
      - mongodb
      - redis
      - minio
    restart: unless-stopped
    networks:
      - trainai-network

  # ================================
  # Flower (Celery监控)
  # ================================
  flower:
    build:
      context: ../backend
      dockerfile: Dockerfile
    container_name: trainai-flower
    command: celery -A app.core.celery flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - DEBUG=true
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - redis
      - celery-worker
    restart: unless-stopped
    networks:
      - trainai-network

  # ================================
  # Nginx (反向代理)
  # ================================
  nginx:
    image: nginx:alpine
    container_name: trainai-nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx.dev.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - api
    restart: unless-stopped
    networks:
      - trainai-network

# ================================
# 数据卷
# ================================
volumes:
  trainai-mongodb-data:
    driver: local
  trainai-redis-data:
    driver: local
  trainai-minio-data:
    driver: local
  trainai-uploads:
    driver: local
  trainai-models:
    driver: local
  trainai-logs:
    driver: local

# ================================
# 网络
# ================================
networks:
  trainai-network:
    driver: bridge
