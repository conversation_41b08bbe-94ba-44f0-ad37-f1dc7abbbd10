@echo off
REM TrainAI 开发环境停止脚本 (Windows)

echo 🛑 停止 TrainAI 开发环境...

REM 进入项目根目录
cd /d "%~dp0\.."

REM 停止服务
echo ⏹️  停止所有服务...
docker-compose -f docker/docker-compose.dev.yml down

REM 检查是否需要清理
if "%1"=="--clean" (
    echo 🧹 清理数据卷...
    docker-compose -f docker/docker-compose.dev.yml down -v
    echo ⚠️  所有数据已清理
)

if "%1"=="--clean-all" (
    echo 🧹 清理数据卷和镜像...
    docker-compose -f docker/docker-compose.dev.yml down -v --rmi all
    echo ⚠️  所有数据和镜像已清理
)

echo ✅ TrainAI 开发环境已停止
echo.
echo 💡 使用说明：
echo   scripts\start-dev.bat           # 重新启动
echo   scripts\stop-dev.bat --clean    # 停止并清理数据
echo   scripts\stop-dev.bat --clean-all # 停止并清理所有
echo.
pause
