<template>
  <div class="profile-page">
    <el-row :gutter="20">
      <!-- 左侧个人信息卡片 -->
      <el-col :xs="24" :lg="8">
        <el-card class="profile-card">
          <div class="profile-header">
            <div class="avatar-container">
              <el-avatar :size="80" :src="avatarUrl || userInfo?.avatar">
                {{ userInfo?.nickname?.charAt(0) }}
              </el-avatar>
              <div class="avatar-overlay" @click="handleAvatarClick">
                <el-icon><Camera /></el-icon>
                <span>更换头像</span>
              </div>
              <el-upload
                ref="avatarUploadRef"
                :show-file-list="false"
                :before-upload="beforeAvatarUpload"
                :http-request="handleAvatarUpload"
                accept="image/*"
                style="display: none;"
              >
                <template #trigger>
                  <div></div>
                </template>
              </el-upload>
            </div>
            <div class="profile-info">
              <h3>{{ userInfo?.nickname || userInfo?.username }}</h3>
              <p>{{ userInfo?.email }}</p>
              <div class="profile-tags">
                <el-tag
                  v-for="role in userInfo?.roles"
                  :key="role"
                  type="primary"
                  size="small"
                >
                  {{ role }}
                </el-tag>
              </div>
            </div>
          </div>
          
          <el-divider />
          
          <div class="profile-stats">
            <div class="stat-item">
              <div class="stat-value">{{ stats.loginCount }}</div>
              <div class="stat-label">登录次数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ stats.lastLogin }}</div>
              <div class="stat-label">最后登录</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ stats.joinDays }}</div>
              <div class="stat-label">加入天数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧信息编辑 -->
      <el-col :xs="24" :lg="16">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>个人信息</span>
              <el-button type="primary" @click="handleEdit" v-if="!isEditing">
                <el-icon><Edit /></el-icon>
                编辑信息
              </el-button>
            </div>
          </template>

          <el-form
            ref="profileFormRef"
            :model="profileForm"
            :rules="profileFormRules"
            label-width="100px"
            :disabled="!isEditing"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="用户名" prop="username">
                  <el-input v-model="profileForm.username" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="昵称" prop="nickname">
                  <el-input v-model="profileForm.nickname" placeholder="请输入昵称" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="邮箱" prop="email">
                  <el-input v-model="profileForm.email" placeholder="请输入邮箱" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="手机号" prop="phone">
                  <el-input v-model="profileForm.phone" placeholder="请输入手机号" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="姓" prop="firstName">
                  <el-input v-model="profileForm.firstName" placeholder="请输入姓" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="名" prop="lastName">
                  <el-input v-model="profileForm.lastName" placeholder="请输入名" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item v-if="isEditing">
              <el-button type="primary" :loading="submitLoading" @click="handleSave">
                保存修改
              </el-button>
              <el-button @click="handleCancel">取消</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 修改密码卡片 -->
        <el-card style="margin-top: 20px;">
          <template #header>
            <span>修改密码</span>
          </template>

          <el-form
            ref="passwordFormRef"
            :model="passwordForm"
            :rules="passwordFormRules"
            label-width="100px"
          >
            <el-form-item label="当前密码" prop="currentPassword">
              <el-input
                v-model="passwordForm.currentPassword"
                type="password"
                placeholder="请输入当前密码"
                show-password
              />
            </el-form-item>

            <el-form-item label="新密码" prop="newPassword">
              <el-input
                v-model="passwordForm.newPassword"
                type="password"
                placeholder="请输入新密码"
                show-password
              />
            </el-form-item>

            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input
                v-model="passwordForm.confirmPassword"
                type="password"
                placeholder="请确认新密码"
                show-password
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" :loading="passwordLoading" @click="handleChangePassword">
                修改密码
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { request } from '@/utils/request'
import { formatDate } from '@/utils'

const userStore = useUserStore()

// 用户信息
const userInfo = computed(() => userStore.userInfo)

// 编辑状态
const isEditing = ref(false)
const submitLoading = ref(false)
const passwordLoading = ref(false)

// 表单引用
const profileFormRef = ref<FormInstance>()
const passwordFormRef = ref<FormInstance>()
const avatarUploadRef = ref()

// 头像相关
const avatarUrl = ref('')
const avatarUploading = ref(false)

// 个人信息表单
const profileForm = reactive({
  username: '',
  nickname: '',
  email: '',
  phone: '',
  firstName: '',
  lastName: ''
})

// 密码表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 统计信息
const stats = reactive({
  loginCount: 0,
  lastLogin: '-',
  joinDays: 0
})

// 表单验证规则
const profileFormRules: FormRules = {
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

const passwordFormRules: FormRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 初始化表单数据
const initFormData = () => {
  if (userInfo.value) {
    Object.assign(profileForm, {
      username: userInfo.value.username || '',
      nickname: userInfo.value.nickname || '',
      email: userInfo.value.email || '',
      phone: userInfo.value.phone || '',
      firstName: userInfo.value.firstName || '',
      lastName: userInfo.value.lastName || ''
    })
  }
}

// 编辑信息
const handleEdit = () => {
  isEditing.value = true
  initFormData()
}

// 保存修改
const handleSave = async () => {
  if (!profileFormRef.value) return

  try {
    await profileFormRef.value.validate()
    submitLoading.value = true

    // 准备更新数据，包含必要的字段
    const updateData: any = {
      id: userInfo.value?.id,
      username: userInfo.value?.username,
      nickname: profileForm.nickname,
      email: profileForm.email,
      phone: profileForm.phone,
      firstName: profileForm.firstName,
      lastName: profileForm.lastName,
      isActive: userInfo.value?.isActive,
      roles: userInfo.value?.roles || []
    }

    // 保持现有密码不变
    updateData.passwordHash = userInfo.value?.passwordHash || 
                             userInfo.value?.PasswordHash || 
                             userInfo.value?.password_hash || ""

    await request.put(`/users/${userInfo.value?.id}`, updateData)
    
    // 更新本地用户信息
    await userStore.getUserInfo()
    
    ElMessage.success('个人信息更新成功')
    isEditing.value = false
  } catch (error: any) {
    console.error('个人信息更新失败:', error)
    
    // 处理 .NET API 的验证错误格式
    if (error.response?.status === 400) {
      const errorData = error.response.data
      
      if (errorData?.errors) {
        const validationErrors = []
        for (const [field, messages] of Object.entries(errorData.errors)) {
          if (Array.isArray(messages)) {
            validationErrors.push(`${field}: ${messages.join(', ')}`)
          } else {
            validationErrors.push(`${field}: ${messages}`)
          }
        }
        ElMessage.error(`验证失败: ${validationErrors.join('; ')}`)
      } else {
        const errorMsg = errorData?.title || errorData?.message || errorData?.error || '请求参数错误'
        ElMessage.error(`请求失败: ${errorMsg}`)
      }
    } else if (error.response?.data?.message) {
      ElMessage.error(error.response.data.message)
    } else {
      ElMessage.error('更新失败，请重试')
    }
  } finally {
    submitLoading.value = false
  }
}

// 取消编辑
const handleCancel = () => {
  isEditing.value = false
  initFormData()
  profileFormRef.value?.resetFields()
}

// 修改密码
const handleChangePassword = async () => {
  if (!passwordFormRef.value) return

  try {
    await passwordFormRef.value.validate()
    passwordLoading.value = true

    await request.post('/auth/change-password', {
      currentPassword: passwordForm.currentPassword,
      newPassword: passwordForm.newPassword
    })

    ElMessage.success('密码修改成功')
    
    // 重置密码表单
    Object.assign(passwordForm, {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    })
    passwordFormRef.value.resetFields()
  } catch (error: any) {
    if (error.response?.data?.message) {
      ElMessage.error(error.response.data.message)
    } else {
      ElMessage.error('密码修改失败，请重试')
    }
  } finally {
    passwordLoading.value = false
  }
}

// 头像上传相关方法
const handleAvatarClick = () => {
  avatarUploadRef.value?.clearFiles()
  const input = avatarUploadRef.value?.$el.querySelector('input[type=file]')
  input?.click()
}

const beforeAvatarUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const handleAvatarUpload = async (options: any) => {
  const { file } = options
  
  try {
    avatarUploading.value = true
    
    // 创建 FormData 对象
    const formData = new FormData()
    formData.append('avatar', file)
    formData.append('userId', userInfo.value?.id || '')
    
    // 上传头像
    const response = await request.upload('/users/avatar', formData)
    
    // 更新头像 URL
    avatarUrl.value = response.data.data?.avatarUrl || response.data.data?.url
    
    // 更新用户信息
    await userStore.getUserInfo()
    
    ElMessage.success('头像上传成功')
  } catch (error: any) {
    console.error('头像上传失败:', error)
    if (error.response?.data?.message) {
      ElMessage.error(error.response.data.message)
    } else {
      ElMessage.error('头像上传失败，请重试')
    }
  } finally {
    avatarUploading.value = false
  }
}

// 获取用户统计信息
const getUserStats = async () => {
  try {
    // 这里可以调用真实的统计接口
    // const response = await request.get('/users/stats')
    
    // 模拟数据
    stats.loginCount = 156
    stats.lastLogin = formatDate(new Date())
    
    if (userInfo.value?.createdAt) {
      const joinDate = new Date(userInfo.value.createdAt)
      const now = new Date()
      stats.joinDays = Math.floor((now.getTime() - joinDate.getTime()) / (1000 * 60 * 60 * 24))
    }
  } catch (error) {
    console.error('获取用户统计信息失败:', error)
  }
}

onMounted(() => {
  initFormData()
  getUserStats()
})
</script>

<style lang="scss" scoped>
.profile-page {
  .profile-card {
    .profile-header {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .profile-info {
        flex: 1;
        
        h3 {
          margin: 0 0 8px 0;
          font-size: 20px;
          color: var(--el-text-color-primary);
        }
        
        p {
          margin: 0 0 12px 0;
          color: var(--el-text-color-regular);
          font-size: 14px;
        }
        
        .profile-tags {
          display: flex;
          gap: 8px;
          flex-wrap: wrap;
        }
      }
    }
    
    .profile-stats {
      display: flex;
      justify-content: space-around;
      
      .stat-item {
        text-align: center;
        
        .stat-value {
          font-size: 20px;
          font-weight: bold;
          color: var(--el-color-primary);
          margin-bottom: 4px;
        }
        
        .stat-label {
          font-size: 12px;
          color: var(--el-text-color-regular);
        }
      }
    }
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .avatar-container {
    position: relative;
    cursor: pointer;
    
    &:hover .avatar-overlay {
      opacity: 1;
    }
    
    .avatar-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 50%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s;
      color: white;
      font-size: 12px;
      
      .el-icon {
        font-size: 20px;
        margin-bottom: 4px;
      }
      
      span {
        font-size: 10px;
        text-align: center;
        line-height: 1.2;
      }
    }
  }
}
</style>