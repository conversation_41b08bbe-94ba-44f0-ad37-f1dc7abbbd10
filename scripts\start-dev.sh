#!/bin/bash

# TrainAI 开发环境启动脚本

set -e

echo "🚀 启动 TrainAI 开发环境..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

# 检查docker-compose是否存在
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose 未安装"
    exit 1
fi

# 进入项目根目录
cd "$(dirname "$0")/.."

# 创建.env文件（如果不存在）
if [ ! -f .env ]; then
    echo "📝 创建 .env 文件..."
    cp .env.example .env
    echo "✅ .env 文件已创建，请根据需要修改配置"
fi

# 构建并启动服务
echo "🔨 构建并启动服务..."
docker-compose -f docker/docker-compose.dev.yml up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose -f docker/docker-compose.dev.yml ps

# 显示服务URL
echo ""
echo "🎉 TrainAI 开发环境启动成功！"
echo ""
echo "📋 服务地址："
echo "  🌐 API服务:      http://localhost:8000"
echo "  📚 API文档:      http://localhost:8000/docs"
echo "  🔧 Nginx代理:    http://localhost:80"
echo "  🌸 Flower监控:   http://localhost:5555"
echo "  🗄️  MongoDB:      mongodb://localhost:27017"
echo "  🔴 Redis:        redis://localhost:6379"
echo "  📦 MinIO:        http://localhost:9001"
echo ""
echo "🔑 默认登录信息："
echo "  用户名: admin"
echo "  密码:   admin123"
echo ""
echo "📝 查看日志: docker-compose -f docker/docker-compose.dev.yml logs -f"
echo "🛑 停止服务: docker-compose -f docker/docker-compose.dev.yml down"
echo ""
