#!/bin/bash

# TrainAI 开发环境停止脚本

set -e

echo "🛑 停止 TrainAI 开发环境..."

# 进入项目根目录
cd "$(dirname "$0")/.."

# 停止服务
echo "⏹️  停止所有服务..."
docker-compose -f docker/docker-compose.dev.yml down

# 可选：清理数据卷（谨慎使用）
if [ "$1" = "--clean" ]; then
    echo "🧹 清理数据卷..."
    docker-compose -f docker/docker-compose.dev.yml down -v
    echo "⚠️  所有数据已清理"
fi

# 可选：清理镜像
if [ "$1" = "--clean-all" ]; then
    echo "🧹 清理数据卷和镜像..."
    docker-compose -f docker/docker-compose.dev.yml down -v --rmi all
    echo "⚠️  所有数据和镜像已清理"
fi

echo "✅ TrainAI 开发环境已停止"
echo ""
echo "💡 使用说明："
echo "  ./scripts/start-dev.sh           # 重新启动"
echo "  ./scripts/stop-dev.sh --clean    # 停止并清理数据"
echo "  ./scripts/stop-dev.sh --clean-all # 停止并清理所有"
echo ""
