<template>
  <div class="ai-settings">
    <div class="ai-settings__header">
      <h2>AI设置</h2>
      <p>配置您的AI助手参数和偏好设置</p>
    </div>
    
    <el-card class="ai-settings__card">
      <template #header>
        <div class="card-header">
          <span>基础配置</span>
          <el-button type="primary" @click="handleSave" :loading="saving">
            <el-icon><Check /></el-icon>
            保存设置
          </el-button>
        </div>
      </template>
      
      <AIConfigForm
        :config="config"
        :providers="providers"
        @save="handleSaveConfig"
        @test="handleTestConnection"
        ref="configFormRef"
      />
    </el-card>
    
    <el-card class="ai-settings__card">
      <template #header>
        <span>使用统计</span>
      </template>
      
      <div class="ai-settings__stats">
        <div class="stat-item">
          <div class="stat-value">{{ stats.totalConversations }}</div>
          <div class="stat-label">总对话数</div>
        </div>
        
        <div class="stat-item">
          <div class="stat-value">{{ stats.totalMessages }}</div>
          <div class="stat-label">总消息数</div>
        </div>
        
        <div class="stat-item">
          <div class="stat-value">{{ stats.totalTokens }}</div>
          <div class="stat-label">总Token数</div>
        </div>
        
        <div class="stat-item">
          <div class="stat-value">{{ formatDate(stats.lastUsed) }}</div>
          <div class="stat-label">最后使用</div>
        </div>
      </div>
    </el-card>
    
    <el-card class="ai-settings__card">
      <template #header>
        <span>数据管理</span>
      </template>
      
      <div class="ai-settings__data">
        <el-alert
          title="数据清理"
          description="清理操作将永久删除数据，请谨慎操作"
          type="warning"
          :closable="false"
          show-icon
        />
        
        <div class="data-actions">
          <el-button @click="handleExportData">
            <el-icon><Download /></el-icon>
            导出对话数据
          </el-button>
          
          <el-button @click="handleImportData">
            <el-icon><Upload /></el-icon>
            导入对话数据
          </el-button>
          
          <el-button type="danger" @click="handleClearAllData">
            <el-icon><Delete /></el-icon>
            清空所有数据
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAIStore } from '@/stores/ai'
import { storeToRefs } from 'pinia'
import AIConfigForm from '@/components/AIChat/AIConfigForm.vue'

const aiStore = useAIStore()
const { config, providers } = storeToRefs(aiStore)

const configFormRef = ref()
const saving = ref(false)

// 使用统计数据
const stats = ref({
  totalConversations: 0,
  totalMessages: 0,
  totalTokens: 0,
  lastUsed: Date.now()
})

// 初始化
onMounted(async () => {
  await Promise.all([
    aiStore.fetchConfig(),
    aiStore.fetchProviders(),
    loadStats()
  ])
})

// 加载统计数据
const loadStats = async () => {
  try {
    await aiStore.fetchConversations()
    
    stats.value.totalConversations = aiStore.conversations.length
    stats.value.totalMessages = aiStore.conversations.reduce((total, conv) => total + conv.messages.length, 0)
    
    // 简单的token估算
    stats.value.totalTokens = aiStore.conversations.reduce((total, conv) => {
      return total + conv.messages.reduce((msgTotal, msg) => {
        return msgTotal + Math.ceil(msg.content.length / 4) // 简单估算
      }, 0)
    }, 0)
    
    // 获取最后使用时间
    const lastConv = aiStore.conversations.sort((a, b) => b.updatedAt - a.updatedAt)[0]
    if (lastConv) {
      stats.value.lastUsed = lastConv.updatedAt
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 格式化日期
const formatDate = (timestamp: number) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 保存设置
const handleSave = async () => {
  if (configFormRef.value) {
    await configFormRef.value.handleSave()
  }
}

// 保存配置
const handleSaveConfig = async (newConfig: any) => {
  try {
    saving.value = true
    await aiStore.updateConfig(newConfig)
    ElMessage.success('设置保存成功')
  } catch (error) {
    console.error('保存设置失败:', error)
    ElMessage.error('保存设置失败')
  } finally {
    saving.value = false
  }
}

// 测试连接
const handleTestConnection = async (testConfig: any) => {
  return await aiStore.testConnection(testConfig)
}

// 导出数据
const handleExportData = async () => {
  try {
    await aiStore.fetchConversations()
    
    const exportData = {
      conversations: aiStore.conversations,
      config: config.value,
      exportTime: Date.now(),
      version: '1.0'
    }
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    })
    
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `ai-chat-data-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    ElMessage.success('数据导出成功')
  } catch (error) {
    console.error('导出数据失败:', error)
    ElMessage.error('导出数据失败')
  }
}

// 导入数据
const handleImportData = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json'
  
  input.onchange = async (event: any) => {
    const file = event.target.files[0]
    if (!file) return
    
    try {
      const text = await file.text()
      const importData = JSON.parse(text)
      
      if (!importData.conversations || !Array.isArray(importData.conversations)) {
        throw new Error('数据格式不正确')
      }
      
      await ElMessageBox.confirm(
        `确定要导入 ${importData.conversations.length} 个对话吗？这将覆盖现有数据。`,
        '确认导入',
        { type: 'warning' }
      )
      
      // TODO: 实现导入逻辑
      ElMessage.success('数据导入成功')
      await loadStats()
    } catch (error) {
      console.error('导入数据失败:', error)
      ElMessage.error('导入数据失败')
    }
  }
  
  input.click()
}

// 清空所有数据
const handleClearAllData = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有对话数据吗？此操作不可恢复！',
      '危险操作',
      {
        type: 'error',
        confirmButtonText: '确定清空',
        cancelButtonText: '取消'
      }
    )
    
    // 删除所有对话
    const deletePromises = aiStore.conversations.map(conv => 
      aiStore.deleteConversation(conv.id)
    )
    
    await Promise.all(deletePromises)
    
    ElMessage.success('所有数据已清空')
    await loadStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空数据失败:', error)
      ElMessage.error('清空数据失败')
    }
  }
}
</script>

<style lang="scss" scoped>
.ai-settings {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
}

.ai-settings__header {
  margin-bottom: 24px;
  
  h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
    color: #303133;
  }
  
  p {
    margin: 0;
    color: #909399;
    font-size: 14px;
  }
}

.ai-settings__card {
  margin-bottom: 24px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
  }
}

.ai-settings__stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 24px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  
  .stat-value {
    font-size: 24px;
    font-weight: 600;
    color: #409eff;
    margin-bottom: 8px;
  }
  
  .stat-label {
    font-size: 14px;
    color: #909399;
  }
}

.ai-settings__data {
  .el-alert {
    margin-bottom: 20px;
  }
  
  .data-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .ai-settings {
    padding: 16px;
  }
  
  .ai-settings__stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  
  .data-actions {
    flex-direction: column;
    
    .el-button {
      width: 100%;
    }
  }
}
</style>