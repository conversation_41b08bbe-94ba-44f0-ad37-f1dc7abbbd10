<template>
  <div class="ai-chat">
    <div class="ai-chat__sidebar">
      <ConversationList
        :conversations="conversations"
        :current-id="currentConversation?.id"
        :loading="loading"
        @create="handleCreateConversation"
        @select="handleSelectConversation"
        @delete="handleDeleteConversation"
        @rename="handleRenameConversation"
      />
    </div>
    
    <div class="ai-chat__main">
      <div v-if="!currentConversation" class="ai-chat__welcome">
        <div class="ai-chat__welcome-content">
          <el-icon class="ai-chat__welcome-icon"><ChatDotRound /></el-icon>
          <h2>欢迎使用AI助手</h2>
          <p>选择一个对话开始聊天，或者创建一个新的对话</p>
          <el-button type="primary" @click="handleCreateConversation">
            <el-icon><Plus /></el-icon>
            开始新对话
          </el-button>
        </div>
      </div>
      
      <div v-else class="ai-chat__conversation">
        <div class="ai-chat__header">
          <div class="ai-chat__title">
            <h3>{{ currentConversation.title }}</h3>
            <span class="ai-chat__message-count">
              {{ currentMessages.length }} 条消息
            </span>
          </div>
          
          <div class="ai-chat__actions">
            <el-button size="small" @click="handleClearConversation">
              <el-icon><Delete /></el-icon>
              清空对话
            </el-button>
            
            <el-button size="small" @click="showConfigDialog = true">
              <el-icon><Setting /></el-icon>
              设置
            </el-button>
          </div>
        </div>
        
        <div class="ai-chat__messages" ref="messagesContainer">
          <div v-if="!currentMessages.length" class="ai-chat__empty">
            <el-empty description="开始你的第一条消息吧！" />
          </div>
          
          <div v-else class="ai-chat__message-list">
            <ChatMessage
              v-for="message in currentMessages"
              :key="message.id"
              :message="message"
              @resend="handleResendMessage"
            />
          </div>
        </div>
        
        <ChatInput
          :disabled="!isConfigured"
          :loading="loading"
          :streaming="streaming"
          @send="handleSendMessage"
          @stop="handleStopGeneration"
          ref="chatInputRef"
        />
      </div>
    </div>
    
    <!-- AI配置对话框 -->
    <el-dialog
      v-model="showConfigDialog"
      title="AI配置"
      width="600px"
      :close-on-click-modal="false"
    >
      <AIConfigForm
        :config="config"
        :providers="providers"
        @save="handleSaveConfig"
        @test="handleTestConnection"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, watch } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useAIStore } from '@/stores/ai'
import { storeToRefs } from 'pinia'
import ConversationList from '@/components/AIChat/ConversationList.vue'
import ChatMessage from '@/components/AIChat/ChatMessage.vue'
import ChatInput from '@/components/AIChat/ChatInput.vue'
import AIConfigForm from '@/components/AIChat/AIConfigForm.vue'

const aiStore = useAIStore()
const {
  conversations,
  currentConversation,
  currentMessages,
  config,
  providers,
  loading,
  streaming,
  isConfigured
} = storeToRefs(aiStore)

const messagesContainer = ref<HTMLElement>()
const chatInputRef = ref()
const showConfigDialog = ref(false)

// 初始化
onMounted(async () => {
  await Promise.all([
    aiStore.fetchConversations(),
    aiStore.fetchConfig(),
    aiStore.fetchProviders()
  ])
  
  // 如果没有配置，显示配置对话框
  if (!isConfigured.value) {
    showConfigDialog.value = true
  }
})

// 监听消息变化，自动滚动到底部
watch(currentMessages, () => {
  nextTick(() => {
    scrollToBottom()
  })
}, { deep: true })

// 滚动到底部
const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// 创建新对话
const handleCreateConversation = async () => {
  try {
    await aiStore.createConversation()
    nextTick(() => {
      chatInputRef.value?.focus()
    })
  } catch (error) {
    console.error('创建对话失败:', error)
  }
}

// 选择对话
const handleSelectConversation = async (id: string) => {
  await aiStore.selectConversation(id)
  nextTick(() => {
    scrollToBottom()
    chatInputRef.value?.focus()
  })
}

// 删除对话
const handleDeleteConversation = async (id: string) => {
  await aiStore.deleteConversation(id)
}

// 重命名对话
const handleRenameConversation = async (id: string, title: string) => {
  try {
    const conversation = conversations.value.find(c => c.id === id)
    if (conversation) {
      await aiStore.updateConversation(id, { title })
      conversation.title = title
      ElMessage.success('重命名成功')
    }
  } catch (error) {
    console.error('重命名失败:', error)
    ElMessage.error('重命名失败')
  }
}

// 清空当前对话
const handleClearConversation = async () => {
  try {
    await ElMessageBox.confirm('确定要清空当前对话吗？', '确认清空', {
      type: 'warning'
    })
    
    if (currentConversation.value) {
      currentConversation.value.messages = []
      await aiStore.updateConversation(currentConversation.value.id, {
        messages: [],
        updatedAt: Date.now()
      })
      ElMessage.success('对话已清空')
    }
  } catch {
    // 用户取消
  }
}

// 发送消息
const handleSendMessage = async (content: string) => {
  if (!isConfigured.value) {
    ElMessage.warning('请先配置AI设置')
    showConfigDialog.value = true
    return
  }
  
  await aiStore.sendMessage(content)
}

// 重新发送消息
const handleResendMessage = async (messageId: string) => {
  await aiStore.resendMessage(messageId)
}

// 停止生成
const handleStopGeneration = () => {
  // TODO: 实现停止生成逻辑
  ElMessage.info('停止生成功能待实现')
}

// 保存配置
const handleSaveConfig = async (newConfig: any) => {
  try {
    await aiStore.updateConfig(newConfig)
    showConfigDialog.value = false
  } catch (error) {
    console.error('保存配置失败:', error)
  }
}

// 测试连接
const handleTestConnection = async (testConfig: any) => {
  return await aiStore.testConnection(testConfig)
}
</script>

<style lang="scss" scoped>
.ai-chat {
  display: flex;
  height: 100vh;
  background-color: #fff;
}

.ai-chat__sidebar {
  width: 300px;
  flex-shrink: 0;
}

.ai-chat__main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.ai-chat__welcome {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.ai-chat__welcome-content {
  text-align: center;
  max-width: 400px;
  padding: 40px;
}

.ai-chat__welcome-icon {
  font-size: 64px;
  margin-bottom: 24px;
  opacity: 0.8;
}

.ai-chat__welcome-content h2 {
  font-size: 28px;
  margin-bottom: 16px;
  font-weight: 600;
}

.ai-chat__welcome-content p {
  font-size: 16px;
  margin-bottom: 32px;
  opacity: 0.9;
  line-height: 1.6;
}

.ai-chat__conversation {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.ai-chat__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #fff;
}

.ai-chat__title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.ai-chat__message-count {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
}

.ai-chat__actions {
  display: flex;
  gap: 8px;
}

.ai-chat__messages {
  flex: 1;
  overflow-y: auto;
  background-color: #f8f9fa;
}

.ai-chat__empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px;
}

.ai-chat__message-list {
  padding: 16px 0;
}
</style>